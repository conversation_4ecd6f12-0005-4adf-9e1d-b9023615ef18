App({
  onLaunch: function() {
    var e = this,
      t = wx.getStorageSync("logs") || [];
    t.unshift(Date.now()), wx.setStorageSync("logs", t), wx.getSetting({
      success: function(t) {
        t.authSetting["scope.userInfo"] && wx.getUserInfo({
          success: function(t) {
            e.globalData.userInfo = t.userInfo, console.log(t, "user"), e.userInfoReadyCallback && e.userInfoReadyCallback(t)
          }
        })
      }
    })
  },
  globalData: {
    userInfo: null,
    code: null,
    appKey: "3n5TgKxmMy1kYhuddr2K84o1YyHm",
    activityUrl: "https://zszy-activity.dexfu.cn/projectx/p973acf1a/index.html?appID=81279"
  }
});