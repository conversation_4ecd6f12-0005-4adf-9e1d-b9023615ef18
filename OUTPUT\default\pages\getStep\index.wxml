<view class="webLoginContainer">
    <view class="center_model">
        <button bindgetphonenumber="getPhoneNumber" class="main_btn" openType="getPhoneNumber" wx:if="{{!hadBindPhone}}"></button>
        <button bindtap="handleJumpH5" class="main_btn" wx:if="{{hadBindPhone}}"></button>
        <view class="today_steps">今日步数:{{todayStep}}步</view>
        <view class="back_btn">
            <button bindtap="backH5" class="back_btn_item"></button>
        </view>
    </view>
</view>
