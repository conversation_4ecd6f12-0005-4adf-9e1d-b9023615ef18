getApp();
Page({
  data: {
    shareInfo: {},
    isShowBtn: !1
  },
  onLoad: function(e) {
    this.initWebView(e)
  },
  initWebView: function(e) {
    var o = null == e ? void 0 : e.isShowBtn,
      n = null == e ? void 0 : e.redirect;
    n && this.setData({
      path: decodeURIComponent(n)
    }), this.setData({
      isShowBtn: "undefined" !== o && Bo<PERSON>an(o)
    })
  },
  getMessage: function(e) {
    var o = e.detail.data;
    console.log(o, "data"), o && this.setData({
      share: o[o.length - 1]
    })
  },
  launchAppError: function(e) {
    console.log(e.detail.errMsg, "返回app失败")
  },
  onShareAppMessage: function(e) {
    var o = this.data.share;
    console.log(null == o ? void 0 : o.link, "share.link--------");
    var n = "/pages/webview/index?redirect=" + encodeURIComponent(o.link);
    return {
      title: o.title,
      path: n,
      imageUrl: o.imgUrl,
      success: function(e) {
        console.log(e, "成功"), console.info(e + "成功"), wx.showToast({
          title: "分享成功"
        })
      },
      fail: function(e) {
        console.log(e + "失败")
      },
      complete: function(e) {
        console.log(e, "成功或失败")
      }
    }
  }
});