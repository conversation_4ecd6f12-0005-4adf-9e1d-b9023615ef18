.webLoginContainer {
    background: url("https://yun.duiba.com.cn/polaris/bg.05e8a8ff7480afeb868a2c50ab7df1f9fd8c285d.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    height: 1624rpx;
    left: 50%;
    position: fixed;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 750rpx
}

.backIcon {
    background: url("https://yun.duiba.com.cn/aurora/assets/e95be2683feddb6b20f028cb01f31fe51553545a.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    border-radius: 50%;
    display: inline-block;
    height: 53rpx;
    position: fixed;
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
    width: 53rpx;
    z-index: 9999
}

.backIcon_item {
    height: 100%;
    opacity: 0;
    width: 100%
}

.webLoginContainer .center_model {
    height: 1128rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 688rpx
}

.center_model .today_steps {
    color: #000;
    font-size: 30rpx;
    height: 50rpx;
    left: 0rpx;
    margin: auto;
    position: absolute;
    right: 0rpx;
    text-align: center;
    top: 950rpx;
    width: 500rpx
}

.back_btn {
    background: url("https://yun.duiba.com.cn/aurora/assets/e95be2683feddb6b20f028cb01f31fe51553545a.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    border-radius: 50%;
    display: inline-block;
    height: 53rpx;
    position: fixed;
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
    width: 53rpx;
    z-index: 9999
}

.back_btn_item {
    height: 100%;
    opacity: 0;
    width: 100%
}

.center_model .btn_getphone {
    background: url("https://yun.duiba.com.cn/aurora/assets/44c6244aefb0d22da0e2977f4b3fba74dfee2028.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    top: 220rpx
}

.center_model .btn_getphone,.center_model .btn_getuserinfo {
    height: 103rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translate(-50%);
    transform: translate(-50%);
    width: 311rpx
}

.center_model .btn_getuserinfo {
    background: url("https://yun.duiba.com.cn/aurora/assets/1ad193b01633952519313dc19f7fc9dcf0daf361.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    top: 320rpx
}

.center_model .main_btn {
    background: url("https://yun.duiba.com.cn/polaris/stepBtn.c798d34be4b4eb748336195fa1a145802791407e.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    height: 125rpx;
    left: 155rpx;
    position: absolute;
    top: 820rpx;
    width: 387rpx
}
