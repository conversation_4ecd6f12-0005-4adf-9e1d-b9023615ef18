var e = require("../../@babel/runtime/helpers/objectSpread2"),
  o = require("../../api/index"),
  t = require("../../api/dbrequest"),
  a = getApp();
Page({
  data: {
    uid: "",
    redirect: "",
    hadBindPhone: !1,
    isShowBtn: !1,
    todayStep: 0
  },
  onLoad: function(e) {
    var o = this,
      t = a.globalData.activityUrl + "&channel=xiaochengxu&toTaskPage=1",
      n = null == e ? void 0 : e.isShowBtn,
      i = null == e ? void 0 : e.uid;
    this.setData({
      redirect: t,
      uid: i,
      isShowBtn: <PERSON>olean(n)
    }), this.getStepPower(), wx.login({
      success: function(e) {
        e.code ? (o.setData({
          code: e.code
        }), o.checkPhonePower(e.code, t)) : console.log("获取用户登录态失败！" + e.errMsg)
      }
    })
  },
  launchAppError: function(e) {
    console.log(e.detail.errMsg, "返回app失败")
  },
  getStepPower: function(e) {
    var o = this;
    console.log(e, "eee"), wx.getWeRunData({
      success: function(e) {
        console.log(e, "获取步数权限成功"), o.setData({
          stepPowerInfo: {
            runEncryptedData: null == e ? void 0 : e.encryptedData,
            runIv: null == e ? void 0 : e.iv
          }
        })
      },
      fail: function(e) {
        console.log(e, "获取步数权限失败"), null != e && e.errMsg.includes("auth deny") && wx.showModal({
          title: "请手动开启微信步数授权",
          showCancel: !1,
          content: "1、点击右上角“...”\n2、点击小程序头像名称进入详情页\n3、再次点击右上角“...”\n4、点击“设置”，打开“微信运动步数"
        })
      }
    })
  },
  checkPhonePower: function(e) {
    var t = this;
    (0, o.reqDbCheckHavePower)({
      data: {
        code: e
      }
    }).then((function(e) {
      var o = e || {},
        a = o.data;
      if (o.success) {
        console.log(a, "手机号检测数据====");
        var n = (a || {}).hadBindPhone;
        t.setData({
          hadBindPhone: t.data.uid ? t.data.uid : n
        })
      }
    }))
  },
  getPhoneNumber: function(o) {
    var t = this;
    o.detail.errMsg.includes("fail") ? console.log("用户拒绝手机号授权", o.detail) : wx.login({
      success: function(a) {
        if (a.code) {
          var n = e({
            code: a.code,
            phoneEncryptedData: o.detail.encryptedData,
            phoneIv: o.detail.iv
          }, t.data.stepPowerInfo);
          t.login(n)
        } else console.log("获取用户登录态失败！" + a.errMsg)
      }
    })
  },
  backH5: function() {
    this.data.actUrl ? wx.navigateTo({
      url: "/pages/webview/index?redirect=".concat(encodeURIComponent(t.dbActDomain + this.data.actUrl))
    }) : wx.navigateBack()
  },
  login: function(t) {
    var a = this;
    if (!this.data.isloading) {
      this.setData({
        isloading: !0
      });
      var n = this.data.redirect;
      (0, o.reqDbLoginNew)({
        data: e(e({}, t), {}, {
          redirect: n
        })
      }).then((function(e) {
        a.setData({
          isloading: !1
        });
        var o = e || {};
        o.data;
        o.success ? (console.log(e, "免登后端接口返回"), wx.showToast({
          title: "同步成功",
          icon: "none",
          image: ""
        }), a.setData({
          todayStep: e.data.runData,
          actUrl: e.data.autoLoginUrl
        })) : console.log("获取兑吧免登失败", e)
      }))
    }
  },
  handleJumpH5: function() {
    var o = this;
    wx.login({
      success: function(t) {
        if (t.code) {
          var a = e({
            code: t.code
          }, o.data.stepPowerInfo);
          o.login(a)
        } else console.log("获取用户登录态失败！" + t.errMsg)
      }
    })
  }
});