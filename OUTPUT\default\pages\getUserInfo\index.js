var e = require("../../@babel/runtime/helpers/objectSpread2"),
  a = require("../../api/index"),
  t = require("../../api/dbrequest"),
  o = require("../../utils/util"),
  n = getApp(),
  i = "https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0";
Page({
  data: {
    avater: "",
    theme: wx.getSystemInfoSync().theme,
    username: "",
    redirect: "",
    hadBindPhone: !0,
    uid: ""
  },
  onLoad: function(e) {
    var a = this;
    console.log(e, "options");
    var t = null != e && e.redirect ? decodeURIComponent(null == e ? void 0 : e.redirect) : n.globalData.activityUrl;
    null != e && e.redirect && delete e.redirect, t = (0, o.getUrl)(t, e), console.log(t, "redirect122"), null != e && e.scene && (t = t + "&" + decodeURIComponent(null == e ? void 0 : e.scene)), console.log(decodeURIComponent(null == e ? void 0 : e.scene), "options?.scene"), console.log(t, "redirect123");
    var i = null == e ? void 0 : e.uid;
    this.setData({
      redirect: t,
      uid: i
    }), this.handleUserInfo(e), wx.login({
      success: function(e) {
        e.code ? (a.setData({
          code: e.code
        }), a.checkPhonePower(e.code, t)) : console.log("获取用户登录态失败！" + e.errMsg)
      }
    }), wx.onThemeChange((function(e) {
      a.setData({
        theme: e.theme
      })
    }))
  },
  handleUserInfo: function(e) {
    var a = null == e ? void 0 : e.avater,
      t = null == e ? void 0 : e.username;
    this.setData({
      avater: a || i,
      username: t
    })
  },
  getDefaultUserName: function() {
    return "中意用户".concat(Math.round(1e7 * Math.random()))
  },
  onChooseAvatar: function(e) {
    var t = this;
    console.error("近来没有");
    e.detail.avatarUrl;
    console.log(e, "头像"), wx.getFileSystemManager().readFile({
      filePath: e.detail.avatarUrl,
      encoding: "base64",
      success: function(e) {
        var o = "data:image/png;base64," + e.data;
        (0, a.reqUploadFile)({
          data: {
            img64: o
          }
        }).then((function() {
          var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
          console.log(e, "resttts"), t.setData({
            avater: null == e ? void 0 : e.data
          })
        }))
      }
    })
  },
  onInputChange: function(e) {
    var a;
    this.setData({
      username: null == e || null === (a = e.detail) || void 0 === a ? void 0 : a.value
    })
  },
  checkPhonePower: function(e) {
    var t = this;
    (0, a.reqDbCheckHavePower)({
      data: {
        code: e
      }
    }).then((function(e) {
      var a = e || {},
        o = a.data;
      if (a.success) {
        console.log(o, "手机号检测数据====");
        var n = (o || {}).hadBindPhone;
        t.setData({
          hadBindPhone: t.data.uid ? t.data.uid : n
        })
      }
    }))
  },
  getPhoneNumber: function(e) {
    var a = this;
    e.detail.errMsg.includes("fail") ? console.log("用户拒绝手机号授权", e.detail) : this.data.avater !== i || this.data.username ? this.data.avater === i || this.data.username ? this.data.username && this.data.avater === i ? wx.showToast({
      title: "请上传头像",
      icon: "error"
    }) : wx.login({
      success: function(t) {
        if (t.code) {
          var o = {
            code: t.code,
            phoneEncryptedData: e.detail.encryptedData,
            phoneIv: e.detail.iv,
            nickname: a.data.username || null,
            avatar: a.data.avater === i ? null : a.data.avater
          };
          a.login(o)
        } else console.log("获取用户登录态失败！" + t.errMsg)
      }
    }) : wx.showToast({
      title: "昵称不能为空",
      icon: "error"
    }) : wx.showToast({
      title: "请输入头像昵称",
      icon: "error"
    })
  },
  login: function(o) {
    var n = this;
    if (!this.data.isloading) {
      this.setData({
        isloading: !0
      });
      var i = this.data.redirect + "&changeUserInfo=1";
      console.log(e(e({}, o), {}, {
        redirect: i
      }), "paramparam"), (0, a.reqDbLogin)({
        data: e(e({}, o), {}, {
          redirect: i
        })
      }).then((function(e) {
        n.setData({
          isloading: !1
        });
        var a = e || {},
          o = a.data;
        a.success ? (console.log(e, "免登后端接口返回"), wx.navigateTo({
          url: "/pages/webview/index?redirect=".concat(encodeURIComponent(t.dbActDomain + o))
        })) : console.log("获取兑吧免登失败", e)
      }))
    }
  },
  onsubmit: function() {
    var e = this,
      a = this.data || {},
      t = a.avater,
      o = a.username;
    t === i || o ? o && t === i ? wx.showToast({
      title: "请上传头像",
      icon: "error"
    }) : wx.login({
      success: function(a) {
        if (a.code) {
          var t = {
            code: a.code,
            nickname: e.data.username,
            avatar: e.data.avater === i ? null : e.data.avater
          };
          e.login(t)
        } else console.log("获取用户登录态失败！" + a.errMsg)
      }
    }) : wx.showToast({
      title: "昵称不能为空",
      icon: "error"
    })
  }
});