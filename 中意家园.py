#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
小程序登录模块精简版
作者: Tianxx
版本: v1.0.0
创建日期: 2023-05-25

重要说明：
本模块通过微信中间服务器获取小程序登录code和手机号。
"""

import os
import sys
import json
import time
import logging
import argparse
import requests
from typing import Dict, List, Optional, Any, Union
import random
from urllib.parse import urlparse, parse_qs
import re
import base64
import hashlib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PhonecodeClient:
    """小程序登录客户端类精简版"""
    
    # 默认的小程序APPID
    DEFAULT_APPID = "wx00c581268da691ff"
    
    def __init__(self, server: str, wxid: str, appid: Optional[str] = None, 
                 log_level: int = logging.INFO):
        """初始化客户端
        
        Args:
            server: 微信中间服务器地址，格式为IP:PORT或完整URL
            wxid: 微信ID
            appid: 小程序AppID，默认使用类中定义的DEFAULT_APPID
            log_level: 日志级别，默认为INFO
        """
        # 基本配置
        self.server = server
        self.wxid = wxid
        self.appid = appid if appid else self.DEFAULT_APPID
        
        # 设置日志
        self.logger = logging.getLogger("phonecode")
        self.logger.setLevel(log_level)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
    
    def log_msg(self, msg: str, level: int = logging.INFO) -> None:
        """记录日志消息"""
        if level == logging.DEBUG:
            self.logger.debug(msg)
        elif level == logging.INFO:
            self.logger.info(msg)
        elif level == logging.WARNING:
            self.logger.warning(msg)
        elif level == logging.ERROR:
            self.logger.error(msg)
    
    def common_post(self, url: str, body: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """公共POST请求方法"""
        headers = {
            'accept': 'application/json',
            'content-type': 'application/json'
        }
        
        # 处理URL
        api_url = url
        if url.startswith('/'):
            server_address = self.server
            if not server_address.startswith(('http://', 'https://')):
                server_address = f"http://{server_address}"
            api_url = f"{server_address}/api{url}"
        
        try:
            self.log_msg(f"请求URL: {api_url}", logging.DEBUG)
            self.log_msg(f"请求Body: {body}", logging.DEBUG)
            
            response = requests.post(api_url, json=body, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            self.log_msg(f"响应结果: {result}", logging.DEBUG)
            return result
        except Exception as e:
            self.log_msg(f"请求失败: {str(e)}", logging.ERROR)
            return None
    
    def get_login_code(self) -> Optional[str]:
        """获取小程序登录code"""
        self.log_msg('开始获取小程序登录code...', logging.DEBUG)
        
        try:
            result = self.common_post('/Wxapp/JSLogin', {
                "Appid": self.appid,
                "Wxid": self.wxid
            })
            
            if not result:
                self.log_msg(f"请求获取小程序授权code失败，请检查服务器连接", logging.ERROR)
                return None
                
            if not result.get('Success'):
                error_msg = result.get('Message', '未知错误')
                self.log_msg(f"获取小程序授权code失败: {error_msg}", logging.ERROR)
                return None
            
            code = result.get('Data', {}).get('code')
            if not code:
                self.log_msg("返回数据中没有code字段", logging.ERROR)
                return None
                
            self.log_msg(f"成功获取小程序授权code: {code}", logging.DEBUG)
            return code
        except Exception as e:
            self.log_msg(f"获取code过程出错: {str(e)}", logging.ERROR)
            return None
    
    def get_mobile_number(self) -> Optional[Dict[str, Any]]:
        """获取小程序手机号"""
        self.log_msg('正在获取小程序手机号...', logging.DEBUG)
        
        try:
            result = self.common_post('/Wxapp/GetAllMobile', {
                "Appid": self.appid,
                "Wxid": self.wxid
            })
            
            if not result or not result.get('Success'):
                self.log_msg(f"获取小程序手机号失败: {result.get('Message', '未知错误')}", logging.ERROR)
                return None
            
            mobile_data = result.get('Data', {})
            self.log_msg(f"成功获取小程序手机号", logging.DEBUG)
            return mobile_data
        except Exception as e:
            self.log_msg(f"获取手机号过程出错: {str(e)}", logging.ERROR)
            return None
    
    def login_process(self) -> bool:
        """完整登录流程"""
        # 验证必要参数
        if not self.wxid:
            self.log_msg('错误：未配置WXID', logging.ERROR)
            return False
        
        # 获取code
        code = self.get_login_code()
        if not code:
            return False
        
        # 登录成功
        return True

class ZhongYiJiaYuan:
    def __init__(self, server=None, wxid=None):
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh-Hans;q=0.9",
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": "https://zszy-activity.dexfu.cn/projectx/p973acf1a/index.html?appID=81279&channel=xiaochengxu&from=login&spm=81279.1.1.1"
        })
        
        # 配置信息
        self.config = {
            "appid": "wx00c581268da691ff",  # 小程序appid，可根据实际情况修改
            "project_id": "p973acf1a",      # 项目ID
            "aid": "81279",                 # 活动ID
            "cid": "4287398215"             # 渠道ID
        }
        
        # 微信服务器信息
        self.server = server
        self.wxid = wxid
        
        # 保存登录状态的文件
        self.token_file = "中意家园_tokens.json"
        
        # 加载已有的token
        self.token_info = self.load_token()
        
        # 基础URL
        self.base_url = "https://zszy-activity.dexfu.cn/projectx"
        
        # 任务列表
        self.task_list = []
        
        # 如果提供了服务器和wxid，创建PhonecodeClient实例
        if self.server and self.wxid:
            self.phone_client = PhonecodeClient(self.server, self.wxid, self.config["appid"])
        else:
            self.phone_client = None

    def load_token(self):
        """加载保存的token信息"""
        if os.path.exists(self.token_file):
            try:
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 多账号支持：如果提供了wxid，则查找对应账号的token
                    if self.wxid and self.wxid in data:
                        account_data = data[self.wxid]
                        # 检查token是否过期
                        if time.time() < account_data.get('expire_time', 0):
                            logger.info(f"加载账号 {self.wxid} 已保存的token信息")
                            return account_data
                    # 如果没有提供wxid，但有默认账号
                    elif 'default' in data and not self.wxid:
                        account_data = data['default']
                        # 检查token是否过期
                        if time.time() < account_data.get('expire_time', 0):
                            logger.info("加载默认账号已保存的token信息")
                            return account_data
            except Exception as e:
                logger.error(f"读取token文件失败: {e}")
        return {}

    def save_token(self, token_info):
        """保存token信息"""
        # 设置过期时间为24小时后
        token_info['expire_time'] = int(time.time()) + 86400
        
        # 读取现有的tokens文件
        all_tokens = {}
        if os.path.exists(self.token_file):
            try:
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    all_tokens = json.load(f)
            except Exception:
                all_tokens = {}
        
        # 根据wxid保存token
        if self.wxid:
            all_tokens[self.wxid] = token_info
        else:
            all_tokens['default'] = token_info
            
        try:
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(all_tokens, f, indent=2)
            logger.info(f"token信息已保存到 {self.token_file}")
        except Exception as e:
            logger.error(f"保存token文件失败: {e}")

    def get_wx_code(self):
        """获取微信登录code"""
        # 如果有PhonecodeClient实例，使用它获取code
        if self.phone_client:
            logger.info("正在通过微信中间服务器获取code...")
            code = self.phone_client.get_login_code()
            if code:
                logger.info(f"成功获取code: {code}")
                return code
            else:
                logger.error("通过微信中间服务器获取code失败")
                
        # 如果没有PhonecodeClient或获取失败，回退到手动输入
        logger.info("请手动输入微信登录code")
        code = input("请输入微信登录code（从小程序调试模式获取）: ")
        return code.strip()

    def auto_login(self, code=None):
        """自动登录流程"""
        if self.token_info and 'tokenId' in self.token_info:
            # 验证token是否有效
            if self.check_token_valid():
                logger.info("已有token有效，无需重新登录")
                return True
        
        if not code:
            code = self.get_wx_code()
            
        # 检查code是否有效
        if not code:
            logger.error("无法获取有效的微信登录code，登录失败")
            return False
        
        # 第一步：调用autoLogin接口
        try:
            # 根据抓包数据，正确的URL是81279.activity-42.m.duiba.com.cn
            url = "https://81279.activity-42.m.duiba.com.cn/wechat/zyrs/autoLogin"
            
            # 根据抓包数据，请求是JSON格式，包含code和redirect
            data = {
                "code": code,
                "redirect": "https://zszy-activity.dexfu.cn/projectx/p973acf1a/index.html?appID=81279&channel=xiaochengxu"
            }
            
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN",
                "Referer": "https://servicewechat.com/wx00c581268da691ff/29/page-frame.html",
                "Accept-Encoding": "gzip,compress,br,deflate"
            }
            
            response = self.session.post(url, json=data, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    # 抓包数据显示，返回的是完整的autologin URL路径，需要拼接完整URL
                    autologin_path = result.get("data", "")
                    if autologin_path:
                        # 确保URL是完整的
                        if autologin_path.startswith("/"):
                            autologin_url = f"https://zszy-activity.dexfu.cn{autologin_path}"
                        else:
                            autologin_url = autologin_path
                            
                        logger.info("获取autologinURL成功")
                        # 第二步：访问autologin URL获取token
                        return self.get_token_from_autologin(autologin_url)
                    else:
                        logger.error("autologinURL不存在")
                else:
                    logger.error(f"autoLogin接口返回失败: {result.get('desc')}")
            else:
                logger.error(f"autoLogin接口请求失败: {response.status_code}")
        except Exception as e:
            logger.error(f"登录过程出错: {e}")
        
        return False

    def get_token_from_autologin(self, autologin_url):
        """从autologin URL获取token"""
        try:
            # 注意：这里需要设置allow_redirects=False来捕获302跳转
            response = self.session.get(autologin_url, allow_redirects=False)
            
            # 从响应头中获取Set-Cookie
            cookies = {}
            if response.status_code == 302:
                # 处理所有的Set-Cookie头
                # requests库中，headers是CaseInsensitiveDict，不能用getall或get_all方法
                # 直接获取所有Set-Cookie头的值
                all_cookies = []
                for key, value in response.headers.items():
                    if key.lower() == 'set-cookie':
                        all_cookies.append(value)
                
                for cookie in all_cookies:
                    # 提取tokenId
                    if 'tokenId=' in cookie:
                        token_id = re.search(r'tokenId=([^;]+)', cookie).group(1)
                        cookies['tokenId'] = token_id
                    
                    # 提取_ac
                    if '_ac=' in cookie:
                        ac_value = re.search(r'_ac=([^;]+)', cookie).group(1)
                        cookies['_ac'] = ac_value
                    
                    # 提取其他重要cookie
                    for cookie_name in ['wdata3', 'wdata4', 'w_ts', 'dcustom', 'createdAtToday', 'isNotLoginUser']:
                        if f'{cookie_name}=' in cookie:
                            value = re.search(f'{cookie_name}=([^;]+)', cookie)
                            if value:
                                cookies[cookie_name] = value.group(1)
            
            if 'tokenId' in cookies:
                logger.info("成功获取tokenId")
                # 保存token信息
                self.token_info = cookies
                self.session.cookies.update({k: v for k, v in cookies.items()})
                self.save_token(cookies)
                return True
            else:
                logger.error("未能从响应中获取tokenId")
        except Exception as e:
            logger.error(f"获取token失败: {e}")
        
        return False

    def check_token_valid(self):
        """检查token是否有效"""
        if not self.token_info or 'tokenId' not in self.token_info:
            return False
        
        # 尝试调用一个需要token的接口来验证
        try:
            # 使用index.do接口验证token
            url = f"{self.base_url}/{self.config['project_id']}/dunhuang/index.do"
            params = {
                "user_type": "0",
                "is_from_share": "1",
                "from": "3",
                "_t": str(int(time.time() * 1000))
            }
            
            # 设置所有必要的cookie
            cookies = {
                'tokenId': self.token_info['tokenId'],
                '_ac': self.token_info.get('_ac', 'eyJhaWQiOjgxMjc5LCJjaWQiOjQyODczOTgyMTV9')
            }
            
            # 添加其他可能存在的cookie
            for cookie_name in ['wdata3', 'wdata4', 'w_ts', 'dcustom', 'createdAtToday', 'isNotLoginUser']:
                if cookie_name in self.token_info:
                    cookies[cookie_name] = self.token_info[cookie_name]
            
            self.session.cookies.update(cookies)
            
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    logger.info("token验证成功")
                    return True
                else:
                    logger.warning("token验证失败，接口返回失败")
            else:
                logger.warning(f"token验证失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"验证token时出错: {e}")
        
        return False

    def daily_sign(self):
        """每日签到"""
        if not self.check_token_valid():
            logger.warning("Token无效，请先登录")
            return False
        
        try:
            # 根据抓包数据，正确的签到URL
            url = f"{self.base_url}/{self.config['project_id']}/dunhuang/sign.do"
            params = {
                "token": self.token_info['tokenId'],  # 使用实际的tokenId，不是"test_token"
                "user_type": "0",
                "is_from_share": "1",
                "from": "3",
                "_t": str(int(time.time() * 1000))
            }
            
            # 设置必要的请求头
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1",
                "Accept-Language": "zh-CN,zh-Hans;q=0.9",
                "Accept-Encoding": "gzip, deflate, br"
            }
            
            # 确保所有cookie都已设置
            cookies = {
                'tokenId': self.token_info['tokenId'],
                '_ac': self.token_info.get('_ac', 'eyJhaWQiOjgxMjc5LCJjaWQiOjQyODczOTgyMTV9')
            }
            
            # 添加其他可能存在的cookie
            for cookie_name in ['wdata3', 'wdata4', 'w_ts', 'dcustom', 'createdAtToday', 'isNotLoginUser']:
                if cookie_name in self.token_info:
                    cookies[cookie_name] = self.token_info[cookie_name]
            
            self.session.cookies.update(cookies)
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    logger.info("签到成功")
                    # 检查签到状态
                    self.check_sign_status()
                    return True
                else:
                    logger.warning(f"签到失败: {result.get('message', '未知错误')}")
            else:
                logger.warning(f"签到请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"签到过程出错: {e}")
        
        return False

    def check_sign_status(self):
        """检查签到状态"""
        try:
            url = f"{self.base_url}/{self.config['project_id']}/dunhuang/index.do"
            params = {
                "user_type": "0",
                "is_from_share": "1",
                "from": "3",
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    daily_sign = data.get("dailySign", {})
                    sign_days = daily_sign.get("signDays", 0)
                    today_sign = daily_sign.get("todaySign", False)
                    total_days = daily_sign.get("totalDays", 7)
                    
                    logger.info(f"签到状态: 今日已签到={today_sign}, 已签到天数={sign_days}, 总天数={total_days}")
                    
                    # 检查是否可以开盲盒
                    if sign_days >= 7:
                        logger.info("已满7天，可以开盲盒")
                        self.open_blind_box()
                    
                    return {
                        "today_sign": today_sign,
                        "sign_days": sign_days,
                        "total_days": total_days
                    }
                else:
                    logger.warning(f"获取签到状态失败: {result.get('message', '未知错误')}")
            else:
                logger.warning(f"获取签到状态请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"检查签到状态出错: {e}")
        
        return None

    def get_task_list(self):
        """获取任务列表"""
        try:
            url = f"{self.base_url}/{self.config['project_id']}/task/list.do"
            params = {
                "token": self.token_info['tokenId'],
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.task_list = result.get("data", [])
                    logger.info(f"获取到{len(self.task_list)}个任务")
                    return self.task_list
                else:
                    logger.warning(f"获取任务列表失败: {result.get('message', '未知错误')}")
            else:
                logger.warning(f"获取任务列表请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"获取任务列表出错: {e}")
        
        return []

    def complete_task(self, task_code):
        """完成指定任务"""
        try:
            # 根据图片中显示的任务类型，判断完成方式
            if task_code == "sign":
                # 签到任务，调用签到接口
                return self.daily_sign()
            elif task_code == "invite":
                # 邀请任务，这需要用户手动操作
                logger.info("邀请任务需要手动操作，无法自动完成")
                return False
            elif task_code == "exchange":
                # 能量兑换任务
                return self.exchange_energy()
            elif task_code.startswith("browse_"):
                # 浏览类任务
                return self.complete_browse_task(task_code)
            elif task_code.startswith("watch_"):
                # 观看类任务
                return self.complete_watch_task(task_code)
            else:
                # 尝试通用任务完成接口
                url = f"{self.base_url}/{self.config['project_id']}/task/do.do"
                data = {
                    "code": task_code
                }
                headers = {
                    "Content-Type": "application/json",
                    "Accept": "*/*",
                    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                    "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
                }
                
                # 确保cookie已设置
                self.check_token_valid()
                
                response = self.session.post(url, json=data, headers=headers)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        logger.info(f"任务 {task_code} 完成成功")
                        return True
                    else:
                        logger.warning(f"任务 {task_code} 完成失败: {result.get('message', '未知错误')}")
                else:
                    logger.warning(f"任务完成请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"完成任务出错: {e}")
        
        return False

    def complete_browse_task(self, task_code):
        """完成浏览类任务"""
        # 根据图片中的"浏览中意产品15s"任务
        logger.info(f"模拟浏览任务: {task_code}")
        try:
            # 获取任务详情
            url = f"{self.base_url}/{self.config['project_id']}/task/detail.do"
            params = {
                "code": task_code,
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code != 200 or not response.json().get("success"):
                logger.warning("获取任务详情失败")
                return False
                
            # 延时模拟浏览时间
            time.sleep(15)
            
            # 完成任务
            return self.complete_task(task_code)
        except Exception as e:
            logger.error(f"浏览任务出错: {e}")
        
        return False
        
    def complete_watch_task(self, task_code):
        """完成观看类任务"""
        # 根据图片中的"浏览中意资讯15s"任务
        logger.info(f"模拟观看任务: {task_code}")
        try:
            # 获取任务详情
            url = f"{self.base_url}/{self.config['project_id']}/task/detail.do"
            params = {
                "code": task_code,
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code != 200 or not response.json().get("success"):
                logger.warning("获取任务详情失败")
                return False
                
            # 延时模拟观看时间
            time.sleep(15)
            
            # 完成任务
            return self.complete_task(task_code)
        except Exception as e:
            logger.error(f"观看任务出错: {e}")
        
        return False

    def complete_step_task(self):
        """完成步数任务"""
        # 根据图片中的"5000步走"和"万步走"任务
        logger.info("尝试完成步数任务")
        try:
            # 获取步数任务列表
            url = f"{self.base_url}/{self.config['project_id']}/step/list.do"
            params = {
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code != 200 or not response.json().get("success"):
                logger.warning("获取步数任务列表失败")
                return False
            
            # 上传步数
            url = f"{self.base_url}/{self.config['project_id']}/step/upload.do"
            data = {
                "step": 10000  # 直接上传最大步数
            }
            
            response = self.session.post(url, json=data, headers=headers)
            if response.status_code == 200 and response.json().get("success"):
                logger.info("步数上传成功")
                return True
            else:
                logger.warning("步数上传失败")
                return False
        except Exception as e:
            logger.error(f"步数任务出错: {e}")
        
        return False

    def get_blind_box_status(self):
        """获取盲盒状态"""
        try:
            url = f"{self.base_url}/{self.config['project_id']}/blindBox/index.do"
            params = {
                "user_type": "0",
                "is_from_share": "1",
                "from": "3",
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    remain_draw_times = data.get("remainDrawTimes", 0)
                    task_info = data.get("taskInfo", [])
                    
                    logger.info(f"盲盒状态: 剩余抽奖次数={remain_draw_times}")
                    
                    # 处理任务信息
                    for task in task_info:
                        code = task.get("code")
                        title = task.get("title")
                        completed = task.get("completed")
                        logger.info(f"任务 [{code}] {title} - 完成状态: {completed}")
                    
                    return {
                        "remain_draw_times": remain_draw_times,
                        "task_info": task_info
                    }
                else:
                    logger.warning(f"获取盲盒状态失败: {result.get('message', '未知错误')}")
            else:
                logger.warning(f"获取盲盒状态请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"获取盲盒状态出错: {e}")
        
        return None

    def open_blind_box(self):
        """开启盲盒"""
        # 先检查是否有抽奖次数
        box_status = self.get_blind_box_status()
        if not box_status or box_status.get("remain_draw_times", 0) <= 0:
            logger.warning("没有可用的盲盒抽奖次数")
            return False
        
        try:
            url = f"{self.base_url}/{self.config['project_id']}/blindBox/draw.do"
            params = {
                "token": self.token_info['tokenId'],
                "user_type": "0",
                "is_from_share": "1",
                "from": "3",
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    prize_name = data.get("prizeName", "未知奖品")
                    logger.info(f"盲盒开启成功，获得奖品: {prize_name}")
                    return True
                else:
                    logger.warning(f"盲盒开启失败: {result.get('message', '未知错误')}")
            else:
                logger.warning(f"盲盒开启请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"开启盲盒出错: {e}")
        
        return False

    def exchange_energy(self):
        """兑换能量为抽奖次数"""
        try:
            # 先获取当前能量值
            index_data = self.get_index_data()
            if not index_data:
                return False
            
            energy = index_data.get("remainCredits", 0)
            if energy < 20:
                logger.warning(f"当前能量值 {energy} 不足，需要至少20点能量")
                return False
            
            # 兑换能量
            url = f"{self.base_url}/{self.config['project_id']}/consumeEnergy.do"
            params = {
                "token": self.token_info['tokenId'],
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    logger.info("能量兑换成功，获得1次抽奖机会")
                    return True
                else:
                    logger.warning(f"能量兑换失败: {result.get('message', '未知错误')}")
            else:
                logger.warning(f"能量兑换请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"兑换能量出错: {e}")
        
        return False

    def get_index_data(self):
        """获取首页数据"""
        try:
            url = f"{self.base_url}/{self.config['project_id']}/dunhuang/index.do"
            params = {
                "user_type": "0",
                "is_from_share": "1",
                "from": "3",
                "_t": str(int(time.time() * 1000))
            }
            
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff",
                "Referer": f"https://zszy-activity.dexfu.cn/projectx/{self.config['project_id']}/index.html?appID={self.config['aid']}&channel=xiaochengxu&from=login&spm={self.config['aid']}.1.1.1"
            }
            
            response = self.session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data", {})
                else:
                    logger.warning(f"获取首页数据失败: {result.get('message', '未知错误')}")
            else:
                logger.warning(f"获取首页数据请求失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"获取首页数据出错: {e}")
        
        return None

    def invite_friends(self):
        """邀请好友（模拟）"""
        logger.info("邀请好友需要实际操作，此处仅模拟")
        return False

    def run_all_tasks(self):
        """运行所有任务"""
        # 1. 登录检查
        if not self.check_token_valid():
            if not self.auto_login():
                logger.error("登录失败，无法继续执行任务")
                return False
        
        # 2. 每日签到
        sign_status = self.check_sign_status()
        if sign_status and not sign_status.get("today_sign", False):
            self.daily_sign()
        else:
            logger.info("今日已签到")
        
        # 3. 获取任务列表并完成任务
        task_list = self.get_task_list()
        if task_list:
            for task in task_list:
                code = task.get("code")
                title = task.get("title")
                completed = task.get("completed")
                
                if completed is False:  # 未完成的任务
                    logger.info(f"尝试完成任务: {title} ({code})")
                    self.complete_task(code)
        
        # 4. 获取盲盒状态
        box_status = self.get_blind_box_status()
        if box_status:
            # 处理盲盒相关任务
            for task in box_status.get("task_info", []):
                code = task.get("code")
                title = task.get("title")
                completed = task.get("completed")
                
                if completed is False:  # 未完成的任务
                    if code == "sign":
                        # 签到任务
                        logger.info("检测到未完成的签到任务")
                        # 签到已在前面处理
                    elif code == "invite":
                        # 邀请任务
                        logger.info("检测到未完成的邀请任务，需要手动操作")
                    elif code == "exchange":
                        # 能量兑换任务
                        logger.info("检测到未完成的能量兑换任务")
                        self.exchange_energy()
            
            # 5. 如果有抽奖次数，开盲盒
            if box_status.get("remain_draw_times", 0) > 0:
                logger.info(f"有 {box_status.get('remain_draw_times')} 次抽奖机会，开始抽奖")
                for _ in range(box_status.get("remain_draw_times", 0)):
                    self.open_blind_box()
                    time.sleep(1)  # 避免频繁请求
        
        # 6. 尝试完成步数任务
        self.complete_step_task()
        
        logger.info("所有任务执行完毕")
        return True

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='中意家园小程序自动化工具')
    parser.add_argument('--server', help='微信中间服务器地址，格式为IP:PORT或完整URL')
    parser.add_argument('--wxid', help='微信ID')
    parser.add_argument('--appid', help='小程序AppID')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--get-mobile', action='store_true', help='获取小程序绑定的手机号')
    parser.add_argument('--only-sign', action='store_true', help='仅执行签到操作')
    parser.add_argument('--only-box', action='store_true', help='仅执行开盲盒操作')
    return parser.parse_args()

def main() -> bool:
    """主函数，用于命令行调用"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 获取配置参数 - 优先使用命令行参数，其次使用环境变量
    server = args.server or os.environ.get('PHONECODE_SERVER')
    wxid = args.wxid
    appid = args.appid
    
    # 如果命令行没有提供wxid，尝试从环境变量获取
    wxids = []
    if wxid:
        wxids = [wxid]
    else:
        # 从环境变量获取，支持多个wxid（换行符分隔）
        env_wxid = os.environ.get('TXX_WXID')
        if env_wxid:
            # 分割多个wxid，处理可能的多行格式
            wxids = [x.strip() for x in env_wxid.split('\n') if x.strip()]
    
    # 如果没有提供wxid和server，直接运行ZhongYiJiaYuan的功能
    if not wxids and not server:
        print("未提供微信服务器信息，将使用手动输入code方式登录")
        app = ZhongYiJiaYuan()
        return app.run_all_tasks()
    
    # 确保必要参数存在
    if not server:
        print("错误：未提供微信中间服务器地址，将使用手动输入code方式登录")
        app = ZhongYiJiaYuan()
        return app.run_all_tasks()
    
    # 如果没有wxid，也使用手动输入方式
    if not wxids:
        print("错误：未提供微信ID，将使用手动输入code方式登录")
        app = ZhongYiJiaYuan()
        return app.run_all_tasks()
    
    # 多账号处理
    success_count = 0
    failed_wxids = []
    
    # 显示所有要处理的wxid
    print(f"准备处理 {len(wxids)} 个账号: {', '.join(wxids)}")
    
    for i, current_wxid in enumerate(wxids):
        try:
            print(f"正在处理第 {i+1}/{len(wxids)} 个账号: {current_wxid}")
            
            # 创建中意家园实例
            app = ZhongYiJiaYuan(server, current_wxid)
            
            # 执行任务
            if args.only_sign:
                # 仅执行签到
                if app.auto_login():
                    success = app.daily_sign()
                    if success:
                        success_count += 1
                    else:
                        failed_wxids.append(current_wxid)
                else:
                    print(f"账号 {current_wxid} 登录失败")
                    failed_wxids.append(current_wxid)
            elif args.only_box:
                # 仅执行开盲盒
                if app.auto_login():
                    success = app.open_blind_box()
                    if success:
                        success_count += 1
                    else:
                        failed_wxids.append(current_wxid)
                else:
                    print(f"账号 {current_wxid} 登录失败")
                    failed_wxids.append(current_wxid)
            else:
                # 执行全部任务
                if app.run_all_tasks():
                    success_count += 1
                else:
                    failed_wxids.append(current_wxid)
            
            # 添加账号处理之间的延迟
            if i < len(wxids) - 1:
                time.sleep(2)
                
        except Exception as e:
            print(f"处理账号 {current_wxid} 时发生错误: {str(e)}")
            failed_wxids.append(current_wxid)
    
    print(f'处理完成! 成功: {success_count}/{len(wxids)}')
    if failed_wxids:
        print(f'失败账号: {", ".join(failed_wxids)}')
    
    return success_count > 0

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        sys.exit(1) 