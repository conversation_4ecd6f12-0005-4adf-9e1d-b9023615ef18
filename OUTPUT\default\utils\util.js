var t = function(t) {
  return (t = t.toString())[1] ? t : "0".concat(t)
};
module.exports = {
  formatTime: function(n) {
    var e = n.getFullYear(),
      o = n.getMonth() + 1,
      r = n.getDate(),
      c = n.getHours(),
      a = n.getMinutes(),
      i = n.getSeconds();
    return "".concat([e, o, r].map(t).join("/"), " ").concat([c, a, i].map(t).join(":"))
  },
  getUrl: function(t, n) {
    return t + (t.indexOf("?") < 0 ? "?" : "&") + function(t) {
      var n = "";
      for (var e in t) {
        var o = void 0 !== t[e] ? t[e] : "";
        n += "&".concat(e, "=").concat(encodeURIComponent(o))
      }
      return n ? n.substring(1) : ""
    }(n)
  }
};