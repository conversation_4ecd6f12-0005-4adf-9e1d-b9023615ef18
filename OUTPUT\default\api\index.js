Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.reqUploadFile = exports.reqDbLoginNew = exports.reqDbLogin = exports.reqDbCheckHavePower = void 0;
var e, r = require("../@babel/runtime/helpers/objectSpread2"),
  o = (e = require("./requestFun")) && e.__esModule ? e : {
    default: e
  },
  t = require("./dbrequest");
exports.reqDbLogin = function(e) {
  return (0, o.default)(r(r({}, e), t.dbLoginParam))
};
exports.reqDbCheckHavePower = function(e) {
  var u;
  return console.log(e), (0, o.default)(r(r({}, t.dbCheckHavePower), {}, {
    url: "".concat(t.dbCheckHavePower.url, "?code=").concat(null == e || null === (u = e.data) || void 0 === u ? void 0 : u.code)
  }))
};
exports.reqDbLoginNew = function(e) {
  return (0, o.default)(r(r({}, e), t.dbLoginParamrNew))
};
exports.reqUploadFile = function(e) {
  return (0, o.default)(r(r({}, e), t.uploadFile))
};