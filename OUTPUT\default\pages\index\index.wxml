<view bind:tap="limitAgree" class="webLoginContainer">
    <view class="center_model">
        <button bindtap="getUserInfo" class="btn_getuserinfo"></button>
        <button bindtap="handleJumpH5" class="main_btn"></button>
        <view class="privacyCon">
            <image catch:tap="agreeOperate" class="agreeCon" src="{{agreeImg}}"></image>
            <text catch:tap="readAgree" class="agreeText">同意<text class="agreeText2">《用户隐私协议》</text>
            </text>
        </view>
    </view>
    <privacy-modal catch:myEvent="modalClose" catch:toastEvent="setToast" wx:if="{{showPrivacy}}"></privacy-modal>
    <view catch:tap="gotoAuth" class="fakeBg" wx:if="{{showFakeBg}}">
        <image catch:tap="showRuleModal" class="ruleIcon1" src="https://yun.duiba.com.cn/aurora/assets/4b87b5662a5cf2451ce478af9548ec18bd602752.png"></image>
        <image class="prizeIcon1" src="https://yun.duiba.com.cn/aurora/assets/63c2b2c2307220c11611cd6652747d78a0b0e3c9.png"></image>
    </view>
    <rule-modal catch:myEvent="closeRuleModal" wx:if="{{showRule}}"></rule-modal>
    <auth-modal catch:myEvent="closeAuthModal" catch:setHadBind="setHadBind" isShowBtn="{{isShowBtn}}" redirect="{{redirect}}" wx:if="{{showAuth}}"></auth-modal>
</view>
