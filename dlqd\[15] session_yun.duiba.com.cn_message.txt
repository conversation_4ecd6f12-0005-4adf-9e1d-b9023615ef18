GET /woodpecker/sdk/0.0.36/woodpecker_outer.js h2
host: yun.duiba.com.cn
accept: */*
accept-encoding: gzip, deflate, br
user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff
accept-language: zh-C<PERSON>,zh-Hans;q=0.9
referer: https://zszy-activity.dexfu.cn/



h2 200
last-modified: Tu<PERSON>, 02 Nov 2021 10:12:58 GMT
content-encoding: gzip
server: AliyunOSS
date: Sun, 19 May 2024 02:49:56 GMT
content-type: application/javascript
vary: Accept-Encoding
x-oss-request-id: 664968D47F57C53437535D16
x-oss-object-type: Normal
x-oss-hash-crc64ecma: 487200436619360358
x-oss-storage-class: Standard
content-md5: dkYvXZi/k3rezl632n9smA==
x-oss-server-time: 44
content-length: 684
accept-ranges: bytes
x-nws-log-uuid: 16217219182503941396
x-cache-lookup: Cache Hit
access-control-allow-origin: *
access-control-expose-headers: Content-Length,Range
access-control-allow-methods: GET,HEAD,OPTIONS
access-control-allow-headers: Content-Length,Range
cache-control: max-age=31536000

!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports):"function"==typeof define&&define.amd?define(["exports"],o):o((e="undefined"!=typeof globalThis?globalThis:e||self).woodpecker={})}(this,(function(e){"use strict";var o=[],n=[],t=!1;setTimeout((function(){var e=new XMLHttpRequest;e.responseType="json",e.open("GET","//woodpecker.dui88.com/home/<USER>",!0),e.setRequestHeader("Content-Type","application/json"),e.onreadystatechange=function(){if(4==e.readyState&&200==e.status){if(!e.response||!e.response.success)return;var d=document.createElement("script");d.src=e.response.data.sdkUrl,d.onload=function(){try{t=!0,window.woodpecker.setConfig(r),o.forEach((function(e){return window.woodpecker.onError(e)})),n.forEach((function(e){return window.woodpecker.onUnReject(e)}))}catch(e){console.log(e)}},document.body.appendChild(d)}},e.onerror=function(e){console.log(e)},e.send()}),500),window.addEventListener("error",(function(e){t?window.woodpecker.onError(e):o.push(e)}),!0),window.addEventListener("unhandledrejection",(function(e){t?window.woodpecker.onUnReject(e):n.push(e)}),!0);var r={};e.setConfig=function(e){r.projectId=e.projectId,r.developer=Array.isArray(e.developer)?e.developer:[e.developer]},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=woodpecker_outer.js.map
