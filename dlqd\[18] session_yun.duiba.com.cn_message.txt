GET /js-libs/rem/1.1.3/rem.min.js h2
host: yun.duiba.com.cn
accept: */*
accept-encoding: gzip, deflate, br
user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff
accept-language: zh-C<PERSON>,zh-Hans;q=0.9
referer: https://zszy-activity.dexfu.cn/



h2 200
last-modified: <PERSON><PERSON>, 12 Jan 2021 10:06:47 GMT
content-encoding: gzip
etag: "A89F2155C151E915A8BCD66A5DDEE028"
server: AliyunOSS
date: Sat, 10 May 2025 11:23:48 GMT
content-type: application/javascript
x-oss-request-id: 681F37447F57C5313883E25C
x-oss-object-type: Normal
x-oss-hash-crc64ecma: 4159422146922545550
x-oss-storage-class: Standard
content-md5: qJ8hVcFR6RWovNZqXd7gKA==
x-oss-server-time: 34
content-length: 492
accept-ranges: bytes
x-nws-log-uuid: 8830170266118223911
x-cache-lookup: Cache Hit
access-control-allow-origin: *
access-control-expose-headers: Content-Length,Range
access-control-allow-methods: GET,HEAD,OPTIONS
access-control-allow-headers: Content-Length,Range
cache-control: max-age=31536000

!(function(e,i){var t=e.documentElement,n=navigator.userAgent.match(/iphone|ipod|ipad/gi),a=n?Math.min(i.devicePixelRatio,3):1,m="orientationchange" in window?"orientationchange":"resize";t.dataset.dpr=a;for(var d,l,c=!1,o=e.getElementsByTagName("meta"),r=0;r<o.length;r++){(l=o[r]),"viewport"==l.name&&((c=!0),(d=l))}if(c){d.content="width=device-width,initial-scale=1.0,maximum-scale=1.0, minimum-scale=1.0,user-scalable=no"}else{var o=e.createElement("meta");(o.name="viewport"),(o.content="width=device-width,initial-scale=1.0,maximum-scale=1.0, minimum-scale=1.0,user-scalable=no"),t.firstElementChild.appendChild(o)}var s=function(){var e=t.clientWidth;var dw = window['designWidth'] || 750;e/a>dw&&(e=dw*a),(window.remScale=e/dw),(t.style.fontSize=100*(e/dw)+"px")};s(),e.addEventListener&&i.addEventListener(m,s,!1)})(document,window);
