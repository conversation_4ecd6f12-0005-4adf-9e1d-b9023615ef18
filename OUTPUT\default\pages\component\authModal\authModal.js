var e = require("../../../@babel/runtime/helpers/objectSpread2"),
  t = require("../../../constant/constant"),
  o = require("../../../api/index"),
  i = require("../../../api/dbrequest");
Component({
  data: {
    content: t.agreeText
  },
  properties: {
    onModalClose: function() {},
    redirect: "",
    isShowBtn: !1
  },
  attached: function() {
    console.log(this, this.data.redirect, this.data.isShowBtn)
  },
  methods: {
    closeModal: function() {
      var e = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
      this.triggerEvent("myEvent", {
        closeImg: e
      })
    },
    login: function(t) {
      var n = this,
        a = this.data.redirect;
      this.data.isloading || (this.setData({
        isloading: !0
      }), wx.showLoading(), (0, o.reqDbLogin)({
        data: e(e({}, t), {}, {
          redirect: a
        })
      }).then((function(e) {
        wx.hideLoading(), n.setData({
          isloading: !1
        });
        var t = e || {},
          o = t.data;
        t.success ? (console.log(e, "免登后端接口返回"), wx.navigateTo({
          url: "/pages/webview/index?redirect=".concat(encodeURIComponent(i.dbActDomain + o), "&isShowBtn=").concat(n.data.isShowBtn)
        }), n.closeModal(!1), n.triggerEvent("setHadBind")) : console.log("获取兑吧免登失败", e)
      })))
    },
    doSure: function(e) {
      var t = this;
      e.detail.errMsg.includes("fail") ? console.log("用户拒绝手机号授权", e.detail) : wx.login({
        success: function(o) {
          if (o.code) {
            var i = {
              code: o.code,
              phoneEncryptedData: e.detail.encryptedData,
              phoneIv: e.detail.iv
            };
            t.login(i)
          } else console.log("获取用户登录态失败！" + o.errMsg)
        }
      })
    },
    toastSetting: function() {
      this.triggerEvent("toastEvent", {
        status: !1
      })
    }
  }
});