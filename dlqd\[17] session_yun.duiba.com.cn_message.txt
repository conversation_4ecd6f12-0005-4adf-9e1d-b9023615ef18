GET /spark/v2/spark.base.fz.wxpollyfill.js h2
host: yun.duiba.com.cn
accept: */*
accept-encoding: gzip, deflate, br
user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff
accept-language: zh-C<PERSON>,zh-Hans;q=0.9
referer: https://zszy-activity.dexfu.cn/



h2 200
last-modified: Wed, 20 Jan 2021 11:54:15 GMT
content-encoding: gzip
etag: "308524236ABB1987E337566F217561B2"
server: AliyunOSS
date: Mon, 31 Mar 2025 06:41:24 GMT
content-type: application/javascript
x-oss-request-id: 67EA3914FBB19F3038AEC294
x-oss-object-type: Normal
x-oss-hash-crc64ecma: 3480685479268636250
x-oss-storage-class: Standard
content-md5: MIUkI2q7GYfjN1ZvIXVhsg==
x-oss-server-time: 32
content-length: 454
accept-ranges: bytes
x-nws-log-uuid: 16118270748083938419
x-cache-lookup: Cache Hit
access-control-allow-origin: *
access-control-expose-headers: Content-Length,Range
access-control-allow-methods: GET,HEAD,OPTIONS
access-control-allow-headers: Content-Length,Range
cache-control: max-age=31536000

/*
 * @Author: super
 * @Date: 2021-01-20 17:31:29
 * @LastEditTime: 2021-01-20 19:53:27
 * @LastEditors: super
 * @Description: 
 */
(function () {
  if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
    handleFontSize();
  } else {
    document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
  }
  function handleFontSize() {
    // 设置网页字体为默认大小
    WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 0 });
    // 重写设置网页字体大小的事件
    WeixinJSBridge.on('menu:setfont', function () {
      WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 0 });
    });
  }
})();
window.onload = function() {
  document.body.style.cssText = "-webkit-text-size-adjust: 100% !important;"
}