var e = require("../../@babel/runtime/helpers/objectSpread2"),
  t = require("../../api/index"),
  o = require("../../api/dbrequest"),
  a = require("../../utils/util"),
  i = require("../../constant/constant"),
  s = getApp();
Page({
  data: {
    path: null,
    redirect: "",
    isShowBtn: !1,
    uid: "",
    hadBindPhone: !1,
    urlParam: {},
    isAgree: !1,
    agreeImg: i.isNotAgreeImg,
    showPrivacy: !1,
    canToast: !0,
    showFakeBg: !1,
    showRule: !1,
    showAuth: !1
  },
  onLoad: function(t) {
    var o = this,
      i = decodeURIComponent((null == t ? void 0 : t.scene) || ""),
      n = null == t ? void 0 : t.isShowBtn,
      r = e(e({}, t), {}, {
        channel: "xiaochengxu"
      });
    null != r && r.redirect && delete r.redirect, null != r && r.scene && delete r.scene;
    var c = (0, a.getUrl)(null != t && t.redirect ? decodeURIComponent(null == t ? void 0 : t.redirect) : s.globalData.activityUrl, r);
    i && (c = c + "&" + i, this.setData({
      scene: i
    }));
    var d = null == t ? void 0 : t.uid;
    console.log("首页redirect----》", c), this.setData({
      redirect: c,
      uid: d,
      isShowBtn: n,
      urlParam: r
    }), wx.login({
      success: function(e) {
        e.code ? o.checkPhonePower(e.code) : console.log("获取用户登录态失败！" + e.errMsg)
      }
    }), this.judgeAgreeImg()
  },
  checkPhonePower: function(e) {
    var o = this;
    console.log("检测手机号"), (0, t.reqDbCheckHavePower)({
      data: {
        code: e
      }
    }).then((function(e) {
      var t = e || {},
        a = t.data;
      if (t.success) {
        console.log(a, "手机号检测数据====");
        var i = (a || {}).hadBindPhone;
        o.setData({
          hadBindPhone: o.data.uid ? o.data.uid : i
        })
      }
    }))
  },
  showRuleModal: function() {
    console.error("jimlail"), this.setData({
      showRule: !0
    })
  },
  showPrizeModal: function() {
    wx.showModal({
      title: "奖品说明",
      content: "",
      showCancel: !1
    })
  },
  closeRuleModal: function() {
    this.setData({
      showRule: !1
    })
  },
  gotoAuth: function() {
    this.setData({
      showAuth: !0
    })
  },
  closeAuthModal: function(e) {
    console.log(e, "sasdad"), this.setData({
      showAuth: !1,
      showFakeBg: e.detail.closeImg
    })
  },
  setHadBind: function() {
    this.setData({
      hadBindPhone: !0
    })
  },
  handleJumpH5: function() {
    var e = this;
    this.data.isAgree && (console.error("进来这里没有"), this.data.hadBindPhone ? wx.login({
      success: function(t) {
        if (t.code) {
          var o = {
            code: t.code
          };
          e.login(o)
        } else console.log("获取用户登录态失败！" + t.errMsg)
      }
    }) : this.setData({
      showFakeBg: !0
    }))
  },
  getPhoneNumber: function(e) {
    var t = this;
    console.error("什么鬼 都没有同意 你怎么会进来的", e), e.detail.errMsg.includes("fail") ? console.log("用户拒绝手机号授权", e.detail) : wx.login({
      success: function(o) {
        if (o.code) {
          var a = {
            code: o.code,
            phoneEncryptedData: e.detail.encryptedData,
            phoneIv: e.detail.iv
          };
          t.login(a)
        } else console.log("获取用户登录态失败！" + o.errMsg)
      }
    })
  },
  getUserInfo: function() {
    this.data.isAgree && (console.log(this.data.redirect, "this.data.redirect"), wx.navigateTo({
      url: (0, a.getUrl)("/pages/getUserInfo/index", e(e({}, this.data.urlParam), {}, {
        scene: this.data.scene,
        redirect: this.data.redirect
      }))
    }))
  },
  login: function(a) {
    var i = this,
      s = this.data.redirect;
    this.data.isloading || (this.setData({
      isloading: !0
    }), wx.showLoading(), (0, t.reqDbLogin)({
      data: e(e({}, a), {}, {
        redirect: s
      })
    }).then((function(e) {
      wx.hideLoading(), i.setData({
        isloading: !1
      });
      var t = e || {},
        a = t.data;
      t.success ? (console.log(e, "免登后端接口返回"), wx.navigateTo({
        url: "/pages/webview/index?redirect=".concat(encodeURIComponent(o.dbActDomain + a), "&isShowBtn=").concat(i.data.isShowBtn)
      })) : console.log("获取兑吧免登失败", e)
    })))
  },
  judgeAgreeImg: function() {
    var e = wx.getStorageSync("isAgree"),
      t = i.isNotAgreeImg;
    e && (t = i.isAgreeImg), this.setData({
      agreeImg: t,
      isAgree: e
    })
  },
  agreeOperate: function() {
    var e = wx.getStorageSync("isAgree");
    wx.setStorageSync("isAgree", !e), this.judgeAgreeImg()
  },
  limitAgree: function() {
    console.error("如果你没有阻止冒泡的话 你就会看见我了 怎么办"), this.data.isAgree || wx.showToast({
      title: "请先同意《用户隐私协议》",
      icon: "none"
    })
  },
  readAgree: function() {
    this.setData({
      showPrivacy: !0
    })
  },
  modalClose: function() {
    this.setData({
      showPrivacy: !1
    })
  },
  setToast: function(e) {
    this.setData({
      canToast: e.detail.status
    })
  }
});