.webLoginContainer {
    background: url("https://yun.duiba.com.cn/polaris/minibg.600dc74601450fd91d63f652228bf9f0514157e2.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    left: 50%;
    position: fixed;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.fakeBg,.webLoginContainer {
    height: 1624rpx;
    width: 750rpx
}

.fakeBg {
    background: url("https://yun.duiba.com.cn/aurora/assets/a76bfd0ad2a0589f2ff6f450a2d909a66779afdf.jpg") no-repeat;
    background-size: 750rpx 1624rpx;
    position: absolute
}

.fakeBg .ruleBtn {
    height: 50rpx;
    position: absolute;
    right: 90rpx;
    top: 254rpx;
    width: 80rpx
}

.fakeBg .ruleIcon1 {
    top: 160rpx
}

.fakeBg .prizeIcon1,.fakeBg .ruleIcon1 {
    height: 62rpx;
    left: 20rpx;
    position: absolute;
    width: 62rpx
}

.fakeBg .prizeIcon1 {
    top: 240rpx
}

.webLoginContainer .center_model {
    height: 1128rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 688rpx
}

.center_model .btn_getphone {
    background: url("https://yun.duiba.com.cn/aurora/assets/44c6244aefb0d22da0e2977f4b3fba74dfee2028.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    height: 103rpx;
    left: 50%;
    position: absolute;
    top: 220rpx;
    -webkit-transform: translate(-50%);
    transform: translate(-50%);
    width: 311rpx
}

.center_model .btn_getuserinfo {
    background: url("https://yun.duiba.com.cn/polaris/join_btn.15fcc72ce81e61689cd9c0a550dbd7ade48b1271.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    top: 919rpx
}

.center_model .btn_getuserinfo,.center_model .main_btn {
    height: 125rpx;
    left: 160rpx;
    position: absolute;
    width: 387rpx
}

.center_model .main_btn {
    background: url("https://yun.duiba.com.cn/polaris/userInfo_btn.2fbcf321f7879257e3ae71c8b4008b96acc425dc.png") no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    top: 783rpx
}

.center_model .privacyCon {
    -webkit-align-items: center;
    align-items: center;
    color: #000;
    display: -webkit-flex;
    display: flex;
    font-size: 24rpx;
    -webkit-justify-content: center;
    justify-content: center;
    position: absolute;
    text-align: center;
    top: 1070rpx;
    width: 100%
}

.privacyCon .agreeText .agreeText2 {
    color: #ac1a18
}

.center_model .agreeCon {
    height: 31rpx;
    margin-right: 10rpx;
    vertical-align: middle;
    width: 31rpx
}

.ruleModal {
    height: 100vh;
    position: absolute
}
