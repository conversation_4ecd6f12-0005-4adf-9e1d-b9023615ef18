GET /projectx/p973acf1a/index.html?appID=81279&channel=xiaochengxu&from=login&spm=81279.1.1.1 h2
host: zszy-activity.dexfu.cn
cookie: _ac=eyJhaWQiOjgxMjc5LCJjaWQiOjQyODczOTgyMTV9
cookie: createdAtToday=false
cookie: dcustom=loginTime%253D1751288934784%2526unionId%253Dopx_asqKgszyaJ3MHoHVY974xoko%2526nickname%253Dtxx%2526systemSrc%253DWX%2526avatar%253D%252F%252Fyun.duiba.com.cn%252Fupload%252F20250628%252Fd09e3bf9.png
cookie: isNotLoginUser=false
cookie: tokenId=90d75426b6ed0cc563e77ba7721fab1f
cookie: w_ts=1751288935232
cookie: wdata3=fu4NJvzsSSnRBB8zFuRfTPKtRPsJBqJGksbhKhG34vzj2SzT1ZwVKtTmFV7PajhFjW9J6AmG584SPcejNWqa82XChMiZtXw2JYn6LMuEHiJG5bjxvW2CDgN7VpwnWYXHBHi9FrVUBzF9BayBuuZ5Jhfbz
cookie: wdata4=Nrrn0RyHcBEKSZlVmHGdTenTvUSN/viaj+D1KDHrGmG2haorJeTad1jr3RNYg6xN82FLCNkG33+4ENIVsvekNxxdPju3fyD0KQGceHkmAttDVnF2DELQp5SBNT5qzPaUpHfsAZSjH7EwvX079dtIYDcN57nT2uWzSZD77B48ZBk=
accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff
accept-language: zh-CN,zh-Hans;q=0.9
accept-encoding: gzip, deflate, br



h2 200
content-type: text/html;charset=UTF-8
date: Mon, 30 Jun 2025 13:08:54 GMT
server: Archer
x-service-id: PROJECTX-GATEWAY
x-profile: prodvpc
content-encoding: gzip

<!doctype html><html lang="zh"><head><meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no"/><meta name="theme-color" content="#000000"/><link rel="dns-prefetch" href="//yun.duiba.com.cn"/><link rel="preconnect" href="//embedlog.duiba.com.cn"/><title>中意家园</title>
<script id="monitorjs" src="//yun.duiba.com.cn/woodpecker/sdk/0.0.36/woodpecker_outer.js" ></script>
  <script id="registerMonitor">
   // 烽火台注入前端监控脚本，请勿修改
    try {
        woodpecker.setConfig({
            projectId: "p973acf1a",
            developer: [
              "e0a8f4b37677dfc21d80c9974d18815d"
            ],
        });
    } catch (e) {
        console.error(e);
    }
  </script>
<script>if (localStorage && localStorage.isWebp) {
        document.getElementsByTagName("html")[0].setAttribute("duiba-webp", "true");
      }</script><script src="//res.wx.qq.com/open/js/jweixin-1.6.0.js" crossorigin="anonymous"></script><script src="//yun.duiba.com.cn/spark/v2/spark.base.fz.wxpollyfill.js"></script><script src="//yun.duiba.com.cn/js-libs/rem/1.1.3/rem.min.js"></script><script src="//yun.duiba.com.cn/h5/lib/zepto.min.js"></script><script>"use strict";

var CFG = CFG || {};
CFG.projectId = location.pathname.split("/")[2] || "1";
function getUrlParam(name) {
  var search = window.location.search;
  var matched = search.slice(1).match(new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i"));
  return search.length ? matched && matched[2] : null;
}
CFG.indexUrl = "/projectx/".concat(CFG.projectId, "/index.html");
// 分享页，其实就是首页的openbs，这个$param$是用来replace参数用的 todo
CFG.shareUrl = "/projectx/".concat(CFG.projectId, "/sharepage.html?appKey=3n5TgKxmMy1kYhuddr2K84o1YyHm&openBs=openbs&appID=81279$param$"); //正式
// CFG.shareUrl = `/projectx/${CFG.projectId}/sharepage.html?appKey=2GxE91465aSpFcSmX2RdJKFjF3eK&openBs=openbs&appID=95640$param$`;//线上测试
// 中意小程序appid;
CFG.miniAppid = "wx00c581268da691ff";
CFG.miniUserName = "gh_10a06170e419";
// 小程序环境
CFG.miniEnv = "release"; //todo正式 release  体验 trial
CFG.toTaskPage = getUrlParam("toTaskPage") == '1';
CFG.channel = getUrlParam("channel") || sessionStorage.getItem("channel");
sessionStorage.setItem("channel", CFG.channel);
CFG.appID = "81279";
if (!getUrlParam("appID")) {
  // alert("【警告】检测到活动url中没有appID参数\n缺少该参数会导致埋点、分享、app信息获取错误。")
}
//直接前往任务页弹toast
CFG.toTaskPage = getUrlParam("toTaskPage") == '1';
//是否修改名字
CFG.changeUserInfo = getUrlParam("changeUserInfo") == '1';</script><style>@font-face {
        font-family: "ali";
        src: url("//yun.duiba.com.cn/aurora/assets/fcd7b19b2f184f7675708e8016e8050289dd2b21.ttf");
      }</style><link href="//yun.duiba.com.cn/spark/v2/temp_base/1751006239453/styles/vendors.0297339d2e287681c00e.css" rel="stylesheet"><link href="//yun.duiba.com.cn/spark/v2/temp_base/1751006239453/styles/main.0297339d2e287681c00e.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"></div><script>"use strict";

CFG.___G___ = 'origin%09git%40gitlab2.dui88.com%3Asparkprojects%2F20241219_ZYSmallLion.git%20(fetch)%3Borigin%09git%40gitlab2.dui88.com%3Asparkprojects%2F20241219_ZYSmallLion.git%20(push)%3B20250528_v3%3Bweiyan%3B';</script><script src="//yun.duiba.com.cn/spark/v2/temp_base/1751006239453/js/runtime-main.0b2c2bf8.js" crossorigin="anonymous"></script><script src="//yun.duiba.com.cn/spark/v2/temp_base/1751006239453/js/vendors.125874a4.js" crossorigin="anonymous"></script><script src="//yun.duiba.com.cn/spark/v2/temp_base/1751006239453/js/main.47d576e1.js" crossorigin="anonymous"></script></body></html>