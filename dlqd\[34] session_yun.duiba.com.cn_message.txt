GET /woodpecker/sdk/0.0.61/woodpecker.js h2
host: yun.duiba.com.cn
accept: */*
accept-encoding: gzip, deflate, br
user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff
accept-language: zh-CN,zh-Hans;q=0.9
referer: https://zszy-activity.dexfu.cn/



h2 200
last-modified: Thu, 13 Jul 2023 02:00:42 GMT
content-encoding: gzip
server: AliyunOSS
date: Wed, 22 May 2024 11:49:16 GMT
content-type: application/javascript
vary: Accept-Encoding
x-oss-request-id: 664DDBBCE876133832E6EC97
x-oss-object-type: Normal
x-oss-hash-crc64ecma: 34091044203280935
x-oss-storage-class: Standard
content-md5: H6hueBA1GJ5TojD7NEX4Ng==
x-oss-server-time: 53
content-length: 13818
accept-ranges: bytes
x-nws-log-uuid: 15557914608170834782
x-cache-lookup: Cache Hit
access-control-allow-origin: *
access-control-expose-headers: Content-Length,Range
access-control-allow-methods: GET,HEAD,OPTIONS
access-control-allow-headers: Content-Length,Range
cache-control: max-age=31536000

!function(e,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports,require("crypto")):"function"==typeof define&&define.amd?define(["exports","crypto"],i):i((e="undefined"!=typeof globalThis?globalThis:e||self).woodpecker={},e.crypto)}(this,(function(e,i){"use strict";function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=n(i),t={sdkVersion:"0.0.61",trackUrl:"//woodpecker.dui88.com/report",projectId:"没设置",developer:["fcfd62083b52fca8ab743ce305ba3905"],allowFileName:[],allowEmptyFileName:!0,allowResourceError:!1};console.log("%cWoodpecker SDK Version: "+t.sdkVersion,"color:green");var o="function",s="object",a="string",u="model",l="name",c="type",d="vendor",f="version",p="console",w="mobile",g="tablet",b="smarttv",m="wearable",h="embedded",v={extend:function(e,i){var n={};for(var r in e)i[r]&&i[r].length%2==0?n[r]=i[r].concat(e[r]):n[r]=e[r];return n},has:function(e,i){return typeof e===a&&-1!==i.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()},major:function(e){return typeof e===a?e.replace(/[^\d\.]/g,"").split(".")[0]:void 0},trim:function(e,i){return e=e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),void 0===i?e:e.substring(0,255)}},y={rgx:function(e,i){for(var n,r,t,a,u,l,c=0;c<i.length&&!u;){var d=i[c],f=i[c+1];for(n=r=0;n<d.length&&!u;)if(u=d[n++].exec(e))for(t=0;t<f.length;t++)l=u[++r],typeof(a=f[t])===s&&a.length>0?2==a.length?typeof a[1]==o?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3==a.length?typeof a[1]!==o||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):void 0:this[a[0]]=l?a[1].call(this,l,a[2]):void 0:4==a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):void 0):this[a]=l||void 0;c+=2}},str:function(e,i){for(var n in i)if(typeof i[n]===s&&i[n].length>0){for(var r=0;r<i[n].length;r++)if(v.has(i[n][r],e))return"?"===n?void 0:n}else if(v.has(i[n],e))return"?"===n?void 0:n;return e}},x={browser:{oldSafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}},oldEdge:{version:{.1:"12.",21:"13.",31:"14.",39:"15.",41:"16.",42:"17.",44:"18."}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},_={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[l,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[l,"Edge"]],[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]{3,6})\b.+version\/([\w\.-]+)/i,/(opera)(?:.+version\/|[\/\s]+)([\w\.]+)/i],[l,f],[/opios[\/\s]+([\w\.]+)/i],[f,[l,"Opera Mini"]],[/\sopr\/([\w\.]+)/i],[f,[l,"Opera"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(ba?idubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,/(rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i,/(weibo)__([\d\.]+)/i],[l,f],[/(?:[\s\/]uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[f,[l,"UCBrowser"]],[/(?:windowswechat)?\sqbcore\/([\w\.]+)\b.*(?:windowswechat)?/i],[f,[l,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[f,[l,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[l,"Konqueror"]],[/trident.+rv[:\s]([\w\.]{1,9})\b.+like\sgecko/i],[f,[l,"IE"]],[/yabrowser\/([\w\.]+)/i],[f,[l,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[l,/(.+)/,"$1 Secure Browser"],f],[/focus\/([\w\.]+)/i],[f,[l,"Firefox Focus"]],[/opt\/([\w\.]+)/i],[f,[l,"Opera Touch"]],[/coc_coc_browser\/([\w\.]+)/i],[f,[l,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[l,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[l,"Opera Coast"]],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[f,[l,"MIUI Browser"]],[/fxios\/([\w\.-]+)/i],[f,[l,"Firefox"]],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[l,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[l,/(.+)/,"$1 Browser"],f],[/(comodo_dragon)\/([\w\.]+)/i],[[l,/_/g," "],f],[/\s(electron)\/([\w\.]+)\ssafari/i,/(tesla)(?:\sqtcarbrowser|\/(20[12]\d\.[\w\.-]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i],[l,f],[/(MetaSr)[\/\s]?([\w\.]+)/i,/(LBBROWSER)/i],[l],[/;fbav\/([\w\.]+);/i],[f,[l,"Facebook"]],[/FBAN\/FBIOS|FB_IAB\/FB4A/i],[[l,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/\s]([\w\.-]+)/i],[l,f],[/\bgsa\/([\w\.]+)\s.*safari\//i],[f,[l,"GSA"]],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[f,[l,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[l,"Chrome WebView"],f],[/droid.+\sversion\/([\w\.]+)\b.+(?:mobile\ssafari|safari)/i],[f,[l,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[l,f],[/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],[f,[l,"Mobile Safari"]],[/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],[f,l],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[l,[f,y.str,x.browser.oldSafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[l,f],[/(navigator|netscape)\/([\w\.-]+)/i],[[l,"Netscape"],f],[/ile\svr;\srv:([\w\.]+)\).+firefox/i],[f,[l,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,/(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[l,f]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus\s10)/i],[u,[d,"Samsung"],[c,g]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy\snexus)/i,/\ssamsung[\s-]([\w-]+)/i,/sec-(sgh\w+)/i],[u,[d,"Samsung"],[c,w]],[/\(ip(?:hone|od);.*Mobile\/(\w*)/i],[u,[d,"iPhone"],[c,w]],[/\(ipad;.*Mobile\/(\w*)/i,/applecoremedia\/[\w\.]+\s\((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[d,"iPad"],[c,g]],[/\b((?:agr|ags[23]|bah2?|sht?)-a?[lw]\d{2})/i],[u,[d,"Huawei"],[c,g]],[/d\/huawei([\w\s-]+)[;\)]/i,/\b(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?|ele-l\d\d)/i,/\b(\w{2,4}-[atu][ln][01259][019])[;\)\s]/i],[u,[d,"Huawei"],[c,w]],[/\b(poco[\s\w]+)(?:\sbuild|\))/i,/\b;\s(\w+)\sbuild\/hm\1/i,/\b(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,/\b(redmi[\s\-_]?(?:note|k)?[\w\s_]+)(?:\sbuild|\))/i,/\b(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i],[[u,/_/g," "],[d,"Xiaomi"],[c,w]],[/\b(mi[\s\-_]?(?:pad)(?:[\w\s_]+))(?:\sbuild|\))/i],[[u,/_/g," "],[d,"Xiaomi"],[c,g]],[/;\s(\w+)\sbuild.+\soppo/i,/\s(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],[u,[d,"OPPO"],[c,w]],[/\svivo\s(\w+)(?:\sbuild|\))/i,/\s(v[12]\d{3}\w?[at])(?:\sbuild|;)/i],[u,[d,"Vivo"],[c,w]],[/\s(rmx[12]\d{3})(?:\sbuild|;)/i],[u,[d,"Realme"],[c,w]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)\b[\w\s]+build\//i,/\smot(?:orola)?[\s-](\w*)/i,/((?:moto[\s\w\(\)]+|xt\d{3,4}|nexus\s6)(?=\sbuild|\)))/i],[u,[d,"Motorola"],[c,w]],[/\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[u,[d,"Motorola"],[c,g]],[/((?=lg)?[vl]k\-?\d{3})\sbuild|\s3\.[\s\w;-]{10}lg?-([06cv9]{3,4})/i],[u,[d,"LG"],[c,g]],[/(lm-?f100[nv]?|nexus\s[45])/i,/lg[e;\s\/-]+((?!browser|netcast)\w+)/i,/\blg(\-?[\d\w]+)\sbuild/i],[u,[d,"LG"],[c,w]],[/(ideatab[\w\-\s]+)/i,/lenovo\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|yt[\d\w-]{6}|tb[\d\w-]{6})/i],[u,[d,"Lenovo"],[c,g]],[/(?:maemo|nokia).*(n900|lumia\s\d+)/i,/nokia[\s_-]?([\w\.-]*)/i],[[u,/_/g," "],[d,"Nokia"],[c,w]],[/droid.+;\s(pixel\sc)[\s)]/i],[u,[d,"Google"],[c,g]],[/droid.+;\s(pixel[\s\daxl]{0,6})(?:\sbuild|\))/i],[u,[d,"Google"],[c,w]],[/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[d,"Sony"],[c,w]],[/sony\stablet\s[ps]\sbuild\//i,/(?:sony)?sgp\w+(?:\sbuild\/|\))/i],[[u,"Xperia Tablet"],[d,"Sony"],[c,g]],[/\s(kb2005|in20[12]5|be20[12][59])\b/i,/\ba000(1)\sbuild/i,/\boneplus\s(a\d{4})[\s)]/i],[u,[d,"OnePlus"],[c,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi)(\sbuild\/|\))/i,/(kf[a-z]+)(\sbuild\/|\)).+silk\//i],[u,[d,"Amazon"],[c,g]],[/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],[[u,"Fire Phone"],[d,"Amazon"],[c,w]],[/\((playbook);[\w\s\),;-]+(rim)/i],[u,d,[c,g]],[/((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10;\s(\w+)/i],[u,[d,"BlackBerry"],[c,w]],[/(?:\b|asus_)(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus\s7|padfone|p00[cj])/i],[u,[d,"ASUS"],[c,g]],[/\s(z[es]6[027][01][km][ls]|zenfone\s\d\w?)\b/i],[u,[d,"ASUS"],[c,w]],[/(nexus\s9)/i],[u,[d,"HTC"],[c,g]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[d,[u,/_/g," "],[c,w]],[/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[u,[d,"Acer"],[c,g]],[/droid.+;\s(m[1-5]\snote)\sbuild/i,/\bmz-([\w-]{2,})/i],[u,[d,"Meizu"],[c,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i,/(microsoft);\s(lumia[\s\w]+)/i,/(lenovo)[_\s-]?([\w-]+)/i,/linux;.+(jolla);/i,/droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[d,u,[c,w]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i,/[;\/]\s?(le[\s\-]+pan)[\s\-]+(\w{1,9})\sbuild/i,/[;\/]\s?(trinity)[\-\s]*(t\d{3})\sbuild/i,/\b(gigaset)[\s\-]+(q\w{1,9})\sbuild/i,/\b(vodafone)\s([\w\s]+)(?:\)|\sbuild)/i],[d,u,[c,g]],[/\s(surface\sduo)\s/i],[u,[d,"Microsoft"],[c,g]],[/droid\s[\d\.]+;\s(fp\du?)\sbuild/i],[u,[d,"Fairphone"],[c,w]],[/\s(u304aa)\sbuild/i],[u,[d,"AT&T"],[c,w]],[/sie-(\w*)/i],[u,[d,"Siemens"],[c,w]],[/[;\/]\s?(rct\w+)\sbuild/i],[u,[d,"RCA"],[c,g]],[/[;\/\s](venue[\d\s]{2,7})\sbuild/i],[u,[d,"Dell"],[c,g]],[/[;\/]\s?(q(?:mv|ta)\w+)\sbuild/i],[u,[d,"Verizon"],[c,g]],[/[;\/]\s(?:barnes[&\s]+noble\s|bn[rt])([\w\s\+]*)\sbuild/i],[u,[d,"Barnes & Noble"],[c,g]],[/[;\/]\s(tm\d{3}\w+)\sbuild/i],[u,[d,"NuVision"],[c,g]],[/;\s(k88)\sbuild/i],[u,[d,"ZTE"],[c,g]],[/;\s(nx\d{3}j)\sbuild/i],[u,[d,"ZTE"],[c,w]],[/[;\/]\s?(gen\d{3})\sbuild.*49h/i],[u,[d,"Swiss"],[c,w]],[/[;\/]\s?(zur\d{3})\sbuild/i],[u,[d,"Swiss"],[c,g]],[/[;\/]\s?((zeki)?tb.*\b)\sbuild/i],[u,[d,"Zeki"],[c,g]],[/[;\/]\s([yr]\d{2})\sbuild/i,/[;\/]\s(dragon[\-\s]+touch\s|dt)(\w{5})\sbuild/i],[[d,"Dragon Touch"],u,[c,g]],[/[;\/]\s?(ns-?\w{0,9})\sbuild/i],[u,[d,"Insignia"],[c,g]],[/[;\/]\s?((nxa|Next)-?\w{0,9})\sbuild/i],[u,[d,"NextBook"],[c,g]],[/[;\/]\s?(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05]))\sbuild/i],[[d,"Voice"],u,[c,w]],[/[;\/]\s?(lvtel\-)?(v1[12])\sbuild/i],[[d,"LvTel"],u,[c,w]],[/;\s(ph-1)\s/i],[u,[d,"Essential"],[c,w]],[/[;\/]\s?(v(100md|700na|7011|917g).*\b)\sbuild/i],[u,[d,"Envizen"],[c,g]],[/[;\/]\s?(trio[\s\w\-\.]+)\sbuild/i],[u,[d,"MachSpeed"],[c,g]],[/[;\/]\s?tu_(1491)\sbuild/i],[u,[d,"Rotor"],[c,g]],[/(shield[\w\s]+)\sbuild/i],[u,[d,"Nvidia"],[c,g]],[/(sprint)\s(\w+)/i],[d,u,[c,w]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[d,"Microsoft"],[c,w]],[/droid\s[\d\.]+;\s(cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[d,"Zebra"],[c,g]],[/droid\s[\d\.]+;\s(ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[d,"Zebra"],[c,w]],[/\s(ouya)\s/i,/(nintendo)\s([wids3utch]+)/i],[d,u,[c,p]],[/droid.+;\s(shield)\sbuild/i],[u,[d,"Nvidia"],[c,p]],[/(playstation\s[345portablevi]+)/i],[u,[d,"Sony"],[c,p]],[/[\s\(;](xbox(?:\sone)?(?!;\sxbox))[\s\);]/i],[u,[d,"Microsoft"],[c,p]],[/smart-tv.+(samsung)/i],[d,[c,b]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[d,"Samsung"],[c,b]],[/(?:linux;\snetcast.+smarttv|lg\snetcast\.tv-201\d)/i],[[d,"LG"],[c,b]],[/(apple)\s?tv/i],[d,[u,"Apple TV"],[c,b]],[/crkey/i],[[u,"Chromecast"],[d,"Google"],[c,b]],[/droid.+aft([\w])(\sbuild\/|\))/i],[u,[d,"Amazon"],[c,b]],[/\(dtv[\);].+(aquos)/i],[u,[d,"Sharp"],[c,b]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[d,v.trim],[u,v.trim],[c,b]],[/[\s\/\(](android\s|smart[-\s]?|opera\s)tv[;\)\s]/i],[[c,b]],[/((pebble))app\/[\d\.]+\s/i],[d,u,[c,m]],[/droid.+;\s(glass)\s\d/i],[u,[d,"Google"],[c,m]],[/droid\s[\d\.]+;\s(wt63?0{2,3})\)/i],[u,[d,"Zebra"],[c,m]],[/(tesla)(?:\sqtcarbrowser|\/20[12]\d\.[\w\.-]+)/i],[d,[c,h]],[/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[u,[c,w]],[/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[u,[c,g]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[c,v.lowerize]],[/(android[\w\.\s\-]{0,9});.+build/i],[u,[d,"Generic"]],[/(phone)/i],[[c,w]]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[l,f],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)(?!.+xbox)/i],[l,[f,y.str,x.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[l,"Windows"],[f,y.str,x.os.windows.version]],[/ip[honead]{2,4}\b(?:.*os\s([\w]+)\slike\smac|;\sopera)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[l,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)(?!.+haiku)/i],[[l,"Mac OS"],[f,/_/g,"."]],[/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/\((series40);/i],[l,f],[/\(bb(10);/i],[f,[l,"BlackBerry"]],[/(?:symbian\s?os|symbos|s60(?=;)|series60)[\/\s-]?([\w\.]*)/i],[f,[l,"Symbian"]],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[l,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[l,"webOS"]],[/crkey\/([\d\.]+)/i],[f,[l,"Chromecast"]],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[l,"Chromium OS"],f],[/(nintendo|playstation)\s([wids345portablevuch]+)/i,/(xbox);\s+xbox\s([^\);]+)/i,/(mint)[\/\s\(\)]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?=\slinux)|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus|raspbian)(?:\sgnu\/linux)?(?:\slinux)?[\/\s-]?(?!chrom|package)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i,/\s([frentopc-]{0,4}bsd|dragonfly)\s?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku)\s(\w+)/i],[l,f],[/(sunos)\s?([\w\.\d]*)/i],[[l,"Solaris"],f],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[l,f]]},k=(new(function(){function e(e,i){this.BROWSER={NAME:l,MAJOR:"major",VERSION:f},this.DEVICE={MODEL:u,VENDOR:d,TYPE:c,CONSOLE:p,MOBILE:w,SMARTTV:b,TABLET:g,WEARABLE:m,EMBEDDED:h},this.OS={NAME:l,VERSION:f},"object"==typeof e&&(i=e,e=void 0),e=e||("undefined"!=typeof window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:""),this._rgxmap=i?v.extend(_,i):_,this.setUA(e)}return e.prototype.getDevice=function(){var e={vendor:void 0,model:void 0,type:void 0};return y.rgx.call(e,this.ua,this._rgxmap.device),e},e.prototype.getBrowser=function(){var e={name:void 0,version:void 0,major:void 0};return y.rgx.call(e,this.ua,this._rgxmap.browser),e.major=v.major(e.version),e},e.prototype.getOS=function(){var e={name:void 0,version:void 0};return y.rgx.call(e,this.ua,this._rgxmap.os),e},e.prototype.setUA=function(e){return this.ua=typeof e===a&&e.length>255?v.trim(e,255):e,this},e.prototype.getUA=function(){return this.ua},e.prototype.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),os:this.getOS(),device:this.getDevice()}},e}())).getResult();function S(e){for(var i=[],n=e.length-1;n>=0;n--){var r=e[n];if(r!==window){var t=r.nodeName.toLocaleLowerCase();r.id?i.push(t+"#"+r.id):r.className&&"string"==typeof r.className?i.push(t+"."+r.className):i.push(t)}}return i.join(" -> ")}var B=["c35 is not defined","ReferenceError: Can't find variable: switchBackground","Can't find variable: switchForeground","Can't find variable: switchBackground","pageAppearProcess is not defined","returnButtonClick is not defined","pageDisappearProcess is not defined","Can't find variable: pageDisappearProcess","Can't find variable: pageAppearProcess","switchBackground is not defined","Can't find variable: sugo","sugo is not defined","ICBCPageTools is not defined","FastClick is not defined","Can't find variable: ICBCPageTools","undefined is not an object (evaluating 'window.webkit.messageHandlers')","undefined is not an object (evaluating 'document.getElementsByTagName('footer')[0].innerHTML')","null is not an object (evaluating 'document.getElementById('returnButton').offsetTop')","ReferenceError: Can't find variable: prompt","[cctvh5-share:jsbridge][showShare] invoke timeout","Can't find variable: WebViewJavascriptBridge","WebViewJavascriptBridge is not defined","null is not an object (evaluating 't.AWAKE_ALLIANCEID')","Can't find variable: cordova","TMFJSBridge is not defined","Failed to start the audio device","Can't find variable: getMenuConfig_Local","Can't find variable: webviewClose","Can't find variable: clientDirectorCallBack","Can't find variable: pluto_gen_10004","pluto_gen_10012 is not defined","Can't find variable: pluto_gen_10008","pluto_static_gen_10010 is not defined","pluto_gen_10006 is not defined","Can't find variable: pluto_gen_10005","pluto_gen_10007 is not defined","pluto_static_gen_10004 is not defined","pluto_static_gen_10007 is not defined","pluto_static_gen_10009 is not defined","Can't find variable: pluto_gen_10006","pluto_gen_10012() is not a function. (In 'pluto_gen_10012()('')', 'pluto_gen_10012()' is undefined)","pluto_gen_10014() is not a function. (In 'pluto_gen_10014()('')', 'pluto_gen_10014()' is undefined)","pluto_gen_10016() is not a function. (In 'pluto_gen_10016()('')', 'pluto_gen_10016()' is undefined)","pluto_gen_10018() is not a function. (In 'pluto_gen_10018()('')', 'pluto_gen_10018()' is undefined)","pluto_gen_10025() is not a function. (In 'pluto_gen_10025()('')', 'pluto_gen_10025()' is undefined)","TypeError undefined is not an object (evaluating 'e.oldURL')","undefined is not an object (evaluating 'e.oldURL')","undefined is not an object (evaluating 'window.webkit.messageHandlers.SugoWKWebViewBindingsTrack.postMessage')"];function M(e){var i=t.allowEmptyFileName,n=t.allowResourceError,r=e.filename,o=void 0===r?"":r,s=e.errType,a=e.message;return"Script error."!==a&&("<anonymous>"!==o&&("resourceError"===s?n:!(o||!i)||!(!function(e){var i=t.allowFileName;if(i&&i.length){for(var n=0;n<i.length;n++)if(e.indexOf(i[n])>-1)return!0;return!1}return!0}(o)||!function(e){return B.indexOf(e)<=-1}(a))))}
/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */var A=function(){return(A=Object.assign||function(e){for(var i,n=1,r=arguments.length;n<r;n++)for(var t in i=arguments[n])Object.prototype.hasOwnProperty.call(i,t)&&(e[t]=i[t]);return e}).apply(this,arguments)},N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function E(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function T(e,i){return e(i={exports:{}},i.exports),i.exports}var O=T((function(e,i){var n;e.exports=n=n||function(e,i){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==N&&N.crypto&&(n=N.crypto),!n)try{n=r.default}catch(e){}var t=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function e(){}return function(i){var n;return e.prototype=i,n=new e,e.prototype=null,n}}(),s={},a=s.lib={},u=a.Base={extend:function(e){var i=o(this);return e&&i.mixIn(e),i.hasOwnProperty("init")&&this.init!==i.init||(i.init=function(){i.$super.init.apply(this,arguments)}),i.init.prototype=i,i.$super=this,i},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var i in e)e.hasOwnProperty(i)&&(this[i]=e[i]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=a.WordArray=u.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=i?n:4*e.length},toString:function(e){return(e||d).stringify(this)},concat:function(e){var i=this.words,n=e.words,r=this.sigBytes,t=e.sigBytes;if(this.clamp(),r%4)for(var o=0;o<t;o++){var s=n[o>>>2]>>>24-o%4*8&255;i[r+o>>>2]|=s<<24-(r+o)%4*8}else for(var a=0;a<t;a+=4)i[r+a>>>2]=n[a>>>2];return this.sigBytes+=t,this},clamp:function(){var i=this.words,n=this.sigBytes;i[n>>>2]&=4294967295<<32-n%4*8,i.length=e.ceil(n/4)},clone:function(){var e=u.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var i=[],n=0;n<e;n+=4)i.push(t());return new l.init(i,e)}}),c=s.enc={},d=c.Hex={stringify:function(e){for(var i=e.words,n=e.sigBytes,r=[],t=0;t<n;t++){var o=i[t>>>2]>>>24-t%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var i=e.length,n=[],r=0;r<i;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new l.init(n,i/2)}},f=c.Latin1={stringify:function(e){for(var i=e.words,n=e.sigBytes,r=[],t=0;t<n;t++){var o=i[t>>>2]>>>24-t%4*8&255;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var i=e.length,n=[],r=0;r<i;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new l.init(n,i)}},p=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},w=a.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=p.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(i){var n,r=this._data,t=r.words,o=r.sigBytes,s=this.blockSize,a=o/(4*s),u=(a=i?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,c=e.min(4*u,o);if(u){for(var d=0;d<u;d+=s)this._doProcessBlock(t,d);n=t.splice(0,u),r.sigBytes-=c}return new l.init(n,c)},clone:function(){var e=u.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=w.extend({cfg:u.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){w.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(i,n){return new e.init(n).finalize(i)}},_createHmacHelper:function(e){return function(i,n){return new g.HMAC.init(e,n).finalize(i)}}});var g=s.algo={};return s}(Math)})),j=T((function(e,i){var n;e.exports=(n=O,function(e){var i=n,r=i.lib,t=r.WordArray,o=r.Hasher,s=i.algo,a=[];!function(){for(var i=0;i<64;i++)a[i]=4294967296*e.abs(e.sin(i+1))|0}();var u=s.MD5=o.extend({_doReset:function(){this._hash=new t.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,i){for(var n=0;n<16;n++){var r=i+n,t=e[r];e[r]=16711935&(t<<8|t>>>24)|4278255360&(t<<24|t>>>8)}var o=this._hash.words,s=e[i+0],u=e[i+1],p=e[i+2],w=e[i+3],g=e[i+4],b=e[i+5],m=e[i+6],h=e[i+7],v=e[i+8],y=e[i+9],x=e[i+10],_=e[i+11],k=e[i+12],S=e[i+13],B=e[i+14],M=e[i+15],A=o[0],N=o[1],E=o[2],T=o[3];A=l(A,N,E,T,s,7,a[0]),T=l(T,A,N,E,u,12,a[1]),E=l(E,T,A,N,p,17,a[2]),N=l(N,E,T,A,w,22,a[3]),A=l(A,N,E,T,g,7,a[4]),T=l(T,A,N,E,b,12,a[5]),E=l(E,T,A,N,m,17,a[6]),N=l(N,E,T,A,h,22,a[7]),A=l(A,N,E,T,v,7,a[8]),T=l(T,A,N,E,y,12,a[9]),E=l(E,T,A,N,x,17,a[10]),N=l(N,E,T,A,_,22,a[11]),A=l(A,N,E,T,k,7,a[12]),T=l(T,A,N,E,S,12,a[13]),E=l(E,T,A,N,B,17,a[14]),A=c(A,N=l(N,E,T,A,M,22,a[15]),E,T,u,5,a[16]),T=c(T,A,N,E,m,9,a[17]),E=c(E,T,A,N,_,14,a[18]),N=c(N,E,T,A,s,20,a[19]),A=c(A,N,E,T,b,5,a[20]),T=c(T,A,N,E,x,9,a[21]),E=c(E,T,A,N,M,14,a[22]),N=c(N,E,T,A,g,20,a[23]),A=c(A,N,E,T,y,5,a[24]),T=c(T,A,N,E,B,9,a[25]),E=c(E,T,A,N,w,14,a[26]),N=c(N,E,T,A,v,20,a[27]),A=c(A,N,E,T,S,5,a[28]),T=c(T,A,N,E,p,9,a[29]),E=c(E,T,A,N,h,14,a[30]),A=d(A,N=c(N,E,T,A,k,20,a[31]),E,T,b,4,a[32]),T=d(T,A,N,E,v,11,a[33]),E=d(E,T,A,N,_,16,a[34]),N=d(N,E,T,A,B,23,a[35]),A=d(A,N,E,T,u,4,a[36]),T=d(T,A,N,E,g,11,a[37]),E=d(E,T,A,N,h,16,a[38]),N=d(N,E,T,A,x,23,a[39]),A=d(A,N,E,T,S,4,a[40]),T=d(T,A,N,E,s,11,a[41]),E=d(E,T,A,N,w,16,a[42]),N=d(N,E,T,A,m,23,a[43]),A=d(A,N,E,T,y,4,a[44]),T=d(T,A,N,E,k,11,a[45]),E=d(E,T,A,N,M,16,a[46]),A=f(A,N=d(N,E,T,A,p,23,a[47]),E,T,s,6,a[48]),T=f(T,A,N,E,h,10,a[49]),E=f(E,T,A,N,B,15,a[50]),N=f(N,E,T,A,b,21,a[51]),A=f(A,N,E,T,k,6,a[52]),T=f(T,A,N,E,w,10,a[53]),E=f(E,T,A,N,x,15,a[54]),N=f(N,E,T,A,u,21,a[55]),A=f(A,N,E,T,v,6,a[56]),T=f(T,A,N,E,M,10,a[57]),E=f(E,T,A,N,m,15,a[58]),N=f(N,E,T,A,S,21,a[59]),A=f(A,N,E,T,g,6,a[60]),T=f(T,A,N,E,_,10,a[61]),E=f(E,T,A,N,p,15,a[62]),N=f(N,E,T,A,y,21,a[63]),o[0]=o[0]+A|0,o[1]=o[1]+N|0,o[2]=o[2]+E|0,o[3]=o[3]+T|0},_doFinalize:function(){var i=this._data,n=i.words,r=8*this._nDataBytes,t=8*i.sigBytes;n[t>>>5]|=128<<24-t%32;var o=e.floor(r/4294967296),s=r;n[15+(t+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),n[14+(t+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),i.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,u=a.words,l=0;l<4;l++){var c=u[l];u[l]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}return a},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,i,n,r,t,o,s){var a=e+(i&n|~i&r)+t+s;return(a<<o|a>>>32-o)+i}function c(e,i,n,r,t,o,s){var a=e+(i&r|n&~r)+t+s;return(a<<o|a>>>32-o)+i}function d(e,i,n,r,t,o,s){var a=e+(i^n^r)+t+s;return(a<<o|a>>>32-o)+i}function f(e,i,n,r,t,o,s){var a=e+(n^(i|~r))+t+s;return(a<<o|a>>>32-o)+i}i.MD5=o._createHelper(u),i.HmacMD5=o._createHmacHelper(u)}(Math),n.MD5)})),C=k.ua,P=k.browser,z=k.os,F=k.device,I={ua:C,browser:P.name+" "+P.version,os:z.name+" "+z.version,device:(F.vendor||"")+" "+(F.model||"")};function R(e){void 0===e&&(e={});var i=A(A({},e),A({projectId:t.projectId,developer:t.developer,sdkVersion:t.sdkVersion,title:document.title,url:location.href,_t:Date.now(),behavior:""},I)),n=i.projectId;i.filename;var r=i.message,o=i.lineno,s=void 0===o?"":o,a=i.colno,u=void 0===a?"":a;return i.errorId=j(n+r+s+u).toString(),i}var G={},q=new(function(){function e(){this.xhr=new XMLHttpRequest,this.xhr.responseType="json"}return e.prototype.send=function(e){if(void 0===e&&(e={}),M(e)){var i=R(e),n=Date.now();if(G[i.errorId]&&n-G[i.errorId]<=3e5)return console.log("同类型距离上一次上报小于五分钟");G[i.errorId]=n;var r=JSON.stringify(i),o=this.xhr;o.open("POST",t.trackUrl,!0),o.setRequestHeader("Content-Type","application/json"),o.onreadystatechange=function(){4==o.readyState&&200==o.status&&console.log("错误日志上传成功",o.response)},o.onerror=function(e){console.log("错误日志上传失败",e)},o.send(r)}else console.log("错误丢弃")},e}()),D="",U=/^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,L=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|capacitor).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,V=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,H=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,W=/\((\S*)(?::(\d+))(?::(\d+))\)/,$=/Minified React error #\d+;/i;function J(e){var i=null,n=0;e&&("number"==typeof e.framesToPop?n=e.framesToPop:$.test(e.message)&&(n=1));try{if(i=function(e){if(!e||!e.stacktrace)return null;for(var i,n=e.stacktrace,r=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,t=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\((.*)\))? in (.*):\s*$/i,o=n.split("\n"),s=[],a=0;a<o.length;a+=2){var u=null;(i=r.exec(o[a]))?u={url:i[2],func:i[3],args:[],line:+i[1],column:null}:(i=t.exec(o[a]))&&(u={url:i[6],func:i[3]||i[4],args:i[5]?i[5].split(","):[],line:+i[1],column:+i[2]}),u&&(!u.func&&u.line&&(u.func=D),s.push(u))}if(!s.length)return null;return{message:K(e),name:e.name,stack:s}}(e))return X(i,n)}catch(e){}try{if(i=function(e){var i,n;if(!e||!e.stack)return null;for(var r,t,o,s=[],a=e.stack.split("\n"),u=0;u<a.length;++u){if(t=U.exec(a[u])){var l=t[2]&&0===t[2].indexOf("native");t[2]&&0===t[2].indexOf("eval")&&(r=W.exec(t[2]))&&(t[2]=r[1],t[3]=r[2],t[4]=r[3]);var c=t[2]&&0===t[2].indexOf("address at ")?t[2].substr("address at ".length):t[2],d=t[1]||D;d=(i=Z(d,c))[0],o={url:c=i[1],func:d,args:l?[t[2]]:[],line:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}else if(t=V.exec(a[u]))o={url:t[2],func:t[1]||D,args:[],line:+t[3],column:t[4]?+t[4]:null};else{if(!(t=L.exec(a[u])))continue;t[3]&&t[3].indexOf(" > eval")>-1&&(r=H.exec(t[3]))?(t[1]=t[1]||"eval",t[3]=r[1],t[4]=r[2],t[5]=""):0!==u||t[5]||void 0===e.columnNumber||(s[0].column=e.columnNumber+1);c=t[3],d=t[1]||D;d=(n=Z(d,c))[0],o={url:c=n[1],func:d,args:t[2]?t[2].split(","):[],line:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}!o.func&&o.line&&(o.func=D),s.push(o)}if(!s.length)return null;return{message:K(e),name:e.name,stack:s}}(e))return X(i,n)}catch(e){}return{message:K(e),name:e&&e.name,stack:[],failed:!0}}var Z=function(e,i){var n=-1!==e.indexOf("safari-extension"),r=-1!==e.indexOf("safari-web-extension");return n||r?[-1!==e.indexOf("@")?e.split("@")[0]:D,n?"safari-extension:"+i:"safari-web-extension:"+i]:[e,i]};function X(e,i){try{return A(A({},e),{stack:e.stack.slice(i)})}catch(i){return e}}function K(e){var i=e&&e.message;return i?i.error&&"string"==typeof i.error.message?i.error.message:i:""}var Y=T((function(e,i){function n(e){return"string"==typeof e}function r(e){return"number"==typeof e}function t(e){return"boolean"==typeof e}function o(e){return Array.isArray(e)}function s(e){return"object"==typeof e&&null!==e}Object.defineProperty(i,"__esModule",{value:!0}),i.StringGuard={type:"string",check:n},i.NumberGuard={type:"number",check:r},i.BooleanGuard={type:"boolean",check:t},i.StringArrayGuard={type:"stringArray",check:function(e){return o(e)&&e.every(n)}},i.NumberArrayGuard={type:"numberArray",check:function(e){return o(e)&&e.every(r)}},i.BooleanArrayGuard={type:"booleanArray",check:function(e){return o(e)&&e.every(t)}},i.StringMapGuard={type:"stringMap",check:function(e){return s(e)&&Object.keys(e).map((function(i){return e[i]})).every(n)}},i.NumberMapGuard={type:"numberMap",check:function(e){return s(e)&&Object.keys(e).map((function(i){return e[i]})).every(r)}},i.BooleanMapGuard={type:"booleanMap",check:function(e){return s(e)&&Object.keys(e).map((function(i){return e[i]})).every(t)}}}));E(Y),Y.StringGuard,Y.NumberGuard,Y.BooleanGuard,Y.StringArrayGuard,Y.NumberArrayGuard,Y.BooleanArrayGuard,Y.StringMapGuard,Y.NumberMapGuard,Y.BooleanMapGuard;var Q=T((function(e,i){var n,r=N&&N.__extends||(n=function(e,i){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)i.hasOwnProperty(n)&&(e[n]=i[n])})(e,i)},function(e,i){function r(){this.constructor=e}n(e,i),e.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)});Object.defineProperty(i,"__esModule",{value:!0});var t=function(e){function i(i){var n=e.call(this,i)||this;return n.name="PropertyUndefinedException",n}return r(i,e),i}(Error);i.PropertyUndefinedException=t;var o=function(e){function i(i){var n=e.call(this,i)||this;return n.name="PropertyTypeMismatchException",n}return r(i,e),i}(Error);i.PropertyTypeMismatchException=o;var s=function(e){function i(i){var n=e.call(this,i)||this;return n.name="PropertyMissingException",n}return r(i,e),i}(Error);i.PropertyMissingException=s,i.createPropertyUndefinedException=function(e,i,n){return new t("Accessing object ("+JSON.stringify(e)+") with accessor ("+i.toString()+") resulted in undefined. Fell back to "+n)},i.createPropertyTypeMismatchException=function(e,i,n,r){return new o("Accessing object ("+JSON.stringify(i)+") with accessor ("+n.toString()+") resulted in undefined. Expected type ("+e+"). Fell back to "+r)},i.createPropertyMissingException=function(e,i,n){return new s("No property was found in object ("+JSON.stringify(e)+") with accessor ("+i.toString()+"). Fell back to "+n)}}));E(Q),Q.PropertyUndefinedException,Q.PropertyTypeMismatchException,Q.PropertyMissingException,Q.createPropertyUndefinedException,Q.createPropertyTypeMismatchException,Q.createPropertyMissingException;var ee=T((function(e,i){function n(e,i,n,r,t){try{var o=n(i);if(void 0===o)throw Q.createPropertyUndefinedException(i,n,r);if(e.check(r)&&e.check(o))return o;throw Q.createPropertyTypeMismatchException(e.type,i,n,r)}catch(e){return t&&e instanceof TypeError?t(Q.createPropertyMissingException(i,n,r)):t&&t(e),r}}Object.defineProperty(i,"__esModule",{value:!0}),i.getString=function(e,i,r,t){return n(Y.StringGuard,e,i,r,t)},i.getNumber=function(e,i,r,t){return n(Y.NumberGuard,e,i,r,t)},i.getBoolean=function(e,i,r,t){return n(Y.BooleanGuard,e,i,r,t)},i.getStringArray=function(e,i,r,t){return n(Y.StringArrayGuard,e,i,r,t)},i.getNumberArray=function(e,i,r,t){return n(Y.NumberArrayGuard,e,i,r,t)},i.getBooleanArray=function(e,i,r,t){return n(Y.BooleanArrayGuard,e,i,r,t)},i.getStringMap=function(e,i,r,t){return n(Y.StringMapGuard,e,i,r,t)},i.getNumberMap=function(e,i,r,t){return n(Y.NumberMapGuard,e,i,r,t)},i.getBooleanMap=function(e,i,r,t){return n(Y.BooleanMapGuard,e,i,r,t)}}));E(ee),ee.getString,ee.getNumber,ee.getBoolean,ee.getStringArray,ee.getNumberArray,ee.getBooleanArray,ee.getStringMap,ee.getNumberMap,ee.getBooleanMap;var ie=T((function(e,i){Object.defineProperty(i,"__esModule",{value:!0}),i.getString=ee.getString,i.getNumber=ee.getNumber,i.getBoolean=ee.getBoolean,i.getStringArray=ee.getStringArray,i.getNumberArray=ee.getNumberArray,i.getBooleanArray=ee.getBooleanArray,i.getStringMap=ee.getStringMap,i.getNumberMap=ee.getNumberMap,i.getBooleanMap=ee.getBooleanMap,i.default=function(e){return{getString:function(i,n,r){return ee.getString(i,n,r,e)},getNumber:function(i,n,r){return ee.getNumber(i,n,r,e)},getBoolean:function(i,n,r){return ee.getBoolean(i,n,r,e)},getStringArray:function(i,n,r){return ee.getStringArray(i,n,r,e)},getNumberArray:function(i,n,r){return ee.getNumberArray(i,n,r,e)},getBooleanArray:function(i,n,r){return ee.getBooleanArray(i,n,r,e)},getStringMap:function(i,n,r){return ee.getStringMap(i,n,r,e)},getNumberMap:function(i,n,r){return ee.getNumberMap(i,n,r,e)},getBooleanMap:function(i,n,r){return ee.getBooleanMap(i,n,r,e)}}}}));E(ie);var ne=ie.getString,re=ie.getNumber;ie.getBoolean,ie.getStringArray,ie.getNumberArray,ie.getBooleanArray,ie.getStringMap,ie.getNumberMap,ie.getBooleanMap,e.onError=function(e){if(e instanceof ErrorEvent){var i=e.type,n=e.message,r=e.error,t=(r||{}).stack,o=void 0===t?"":t,s=J(r),a=ne(s,(function(e){return e.stack[0].url}),e.filename),u=re(s,(function(e){return e.stack[0].line}),e.lineno),l=re(s,(function(e){return e.stack[0].column}),e.colno),c=s.name,d={type:i,stack:o,lineno:u,colno:l,filename:a.split("?")[0],message:s.message||n,errType:c};q.send(d)}else if(e instanceof Event){i=e.type;var f=e.target,p=e.path,w=f.src||f.href,g=decodeURIComponent(w.split("?")[0]);d={type:i,path:S(p),message:"加载 "+g+" 失败",resourceUrl:g,errType:"resourceError"};q.send(d)}},e.onUnReject=function(e){var i=e.type,n=e.reason,r={type:i,stack:null,message:null,errType:null,filename:null,lineno:null,colno:null};if(n instanceof Error){var t=n.message,o=n.stack,s=J(e.reason),a=ne(s,(function(e){return e.stack[0].url}),""),u=re(s,(function(e){return e.stack[0].line}),0),l=re(s,(function(e){return e.stack[0].column}),0);r.stack=o,r.message=s.message||t,r.errType=s.name||o.split(":")[0],r.filename=a.split("?")[0],r.lineno=u,r.colno=l,q.send(r)}},e.setConfig=function(e){var i=e.projectId,n=void 0===i?"没设置":i,r=e.developer,o=void 0===r?[]:r,s=e.allowFileName,a=void 0===s?[]:s,u=e.allowEmptyFileName,l=void 0===u||u,c=e.allowResourceError,d=void 0!==c&&c;t.projectId=n,t.developer=Array.isArray(o)?o:[o],t.allowFileName=Array.isArray(a)?a:[a],t.allowEmptyFileName=l,t.allowResourceError=d},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=woodpecker.js.map
