GET /projectx/p973acf1a/index.html?appID=81279&channel=xiaochengxu&from=login&spm=81279.1.1.1 h2
host: zszy-activity.dexfu.cn
cookie: _ac=eyJhaWQiOjgxMjc5LCJjaWQiOjQyODkwNjM5MTh9
cookie: createdAtToday=false
cookie: dcustom=loginTime%253D1751347321341%2526unionId%253Dopx_asq4qOQFhMUfFKw22_qsk68I%2526nickname%253D%2526systemSrc%253DWX%2526avatar%253D
cookie: isNotLoginUser=false
cookie: tokenId=d62e030fbae57c629cb46f3e42f5d64d
cookie: w_ts=1751347322262
cookie: wdata3=fu4NJvzsSSnRBB8zFuRfTPKtRPsJBqJGkmHgL78YXTkwbjCrKazRx2zjRnP4cCPz3C6QHma9xetbCBBh8XMnVgkBixKnhqY7imtp3bYvLBydf6snJ1AiBZMX4McpHQouisFVCy3vhe6iYYxHjTsa3YrA9
cookie: wdata4=BuMiDVa+3CN+ryJr7tDH6l+Tcvclj578SIbknkVlwsajQIYg668jbA5XFl1PEGdi85t4UrPHqfy2AM+42pCyuJx1+0oAV4E3lbBnhXAm42BxkFuF3dygQ33MCsyQkRTkfncLal41X1NpgXusDvEUAgvyEcp6s5XLHbadnTagf3o=
cookie: _ac=eyJhaWQiOjgxMjc5LCJjaWQiOjQyODkwNjQ2NDZ9
cookie: wdata3=jaH8Bq8XcNMD4rFqSeqaMBDLoissnexH5DxHgAN3DjB6AgF4Gq4atZM1tstEDqke73f7XAE6Ln1zXQAoAzRe4z7goxFDPLJr74F4yWxE39nHfk18bEQbaVfAvT95CFQPP4XBKWatdKqfUTKz9MRuePYgH19mM7YtJY7Vys2b
accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 14_8_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2e) NetType/WIFI Language/zh_CN miniProgram/wx00c581268da691ff
accept-language: zh-cn
accept-encoding: gzip, deflate, br



h2 200
content-type: text/html;charset=UTF-8
date: Tue, 01 Jul 2025 05:22:02 GMT
server: Archer
x-service-id: PROJECTX-GATEWAY
x-profile: prodvpc
content-encoding: gzip

<!doctype html><html lang="zh"><head><meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no"/><meta name="theme-color" content="#000000"/><link rel="dns-prefetch" href="//yun.duiba.com.cn"/><link rel="preconnect" href="//embedlog.duiba.com.cn"/><title>中意家园</title>
<script id="monitorjs" src="//yun.duiba.com.cn/woodpecker/sdk/0.0.36/woodpecker_outer.js" ></script>
  <script id="registerMonitor">
   // 烽火台注入前端监控脚本，请勿修改
    try {
        woodpecker.setConfig({
            projectId: "p973acf1a",
            developer: [
              "e0a8f4b37677dfc21d80c9974d18815d"
            ],
        });
    } catch (e) {
        console.error(e);
    }
  </script>
<script>if (localStorage && localStorage.isWebp) {
        document.getElementsByTagName("html")[0].setAttribute("duiba-webp", "true");
      }</script><script src="//res.wx.qq.com/open/js/jweixin-1.6.0.js" crossorigin="anonymous"></script><script src="//yun.duiba.com.cn/spark/v2/spark.base.fz.wxpollyfill.js"></script><script src="//yun.duiba.com.cn/js-libs/rem/1.1.3/rem.min.js"></script><script src="//yun.duiba.com.cn/h5/lib/zepto.min.js"></script><script>"use strict";

var CFG = CFG || {};
CFG.projectId = location.pathname.split("/")[2] || "1";
function getUrlParam(name) {
  var search = window.location.search;
  var matched = search.slice(1).match(new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i"));
  return search.length ? matched && matched[2] : null;
}
CFG.indexUrl = "/projectx/".concat(CFG.projectId, "/index.html");
// 分享页，其实就是首页的openbs，这个$param$是用来replace参数用的 todo
CFG.shareUrl = "/projectx/".concat(CFG.projectId, "/sharepage.html?appKey=3n5TgKxmMy1kYhuddr2K84o1YyHm&openBs=openbs&appID=81279$param$"); //正式
// CFG.shareUrl = `/projectx/${CFG.projectId}/sharepage.html?appKey=2GxE91465aSpFcSmX2RdJKFjF3eK&openBs=openbs&appID=95640$param$`;//线上测试
// 中意小程序appid;
CFG.miniAppid = "wx00c581268da691ff";
CFG.miniUserName = "gh_10a06170e419";
// 小程序环境
CFG.miniEnv = "release"; //todo正式 release  体验 trial
CFG.toTaskPage = getUrlParam("toTaskPage") == '1';
CFG.channel = getUrlParam("channel") || sessionStorage.getItem("channel");
sessionStorage.setItem("channel", CFG.channel);
CFG.appID = "81279";
if (!getUrlParam("appID")) {
  // alert("【警告】检测到活动url中没有appID参数\n缺少该参数会导致埋点、分享、app信息获取错误。")
}
//直接前往任务页弹toast
CFG.toTaskPage = getUrlParam("toTaskPage") == '1';
//是否修改名字
CFG.changeUserInfo = getUrlParam("changeUserInfo") == '1';</script><style>@font-face {
        font-family: "ali";
        src: url("//yun.duiba.com.cn/aurora/assets/fcd7b19b2f184f7675708e8016e8050289dd2b21.ttf");
      }</style><link href="//yun.duiba.com.cn/spark/v2/temp_base/1751336241767/styles/vendors.54138c8dd60356a8f782.css" rel="stylesheet"><link href="//yun.duiba.com.cn/spark/v2/temp_base/1751336241767/styles/main.54138c8dd60356a8f782.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"></div><script>"use strict";

CFG.___G___ = 'origin%09http%3A%2F%2Fgitlab2.dui88.com%2Fsparkprojects%2F20241219_ZYSmallLion%20(fetch)%3Borigin%09http%3A%2F%2Fgitlab2.dui88.com%2Fsparkprojects%2F20241219_ZYSmallLion%20(push)%3B20250528_v3%3Bwangzhujun%3B';</script><script src="//yun.duiba.com.cn/spark/v2/temp_base/1751336241767/js/runtime-main.fa658288.js" crossorigin="anonymous"></script><script src="//yun.duiba.com.cn/spark/v2/temp_base/1751336241767/js/vendors.ea0ee5a9.js" crossorigin="anonymous"></script><script src="//yun.duiba.com.cn/spark/v2/temp_base/1751336241767/js/main.8881fd5a.js" crossorigin="anonymous"></script></body></html>