Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/objectSpread2"),
  t = require("../@babel/runtime/helpers/asyncToGenerator"),
  n = function() {
    var n = t(e().mark((function t(n) {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return console.log(n, "dataSouce"), e.next = 3, new Promise((function(e, t) {
              wx.request(r(r({}, n), {}, {
                success: function(r) {
                  e(r.data)
                },
                fail: function(e) {
                  console.log("res:失败数据", e), t(e.data)
                }
              }))
            }));
          case 3:
            return e.abrupt("return", e.sent);
          case 4:
          case "end":
            return e.stop()
        }
      }), t)
    })));
    return function(e) {
      return n.apply(this, arguments)
    }
  }();
exports.default = n;